Component: Arm Compiler for Embedded 6.21 Tool: armlink [5ec1fa00]

==============================================================================

Section Cross References

    empty.o(.text.main) refers to pid.o(.text.PID_Init) for PID_Init
    empty.o(.text.main) refers to filter.o(.text.Kalman_Init) for Kalman_Init
    empty.o(.text.main) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for SYSCFG_DL_init
    empty.o(.text.main) refers to hw_timer.o(.text.timer_init) for timer_init
    empty.o(.text.main) refers to empty.o(.text.__NVIC_ClearPendingIRQ) for __NVIC_ClearPendingIRQ
    empty.o(.text.main) refers to empty.o(.text.__NVIC_EnableIRQ) for __NVIC_EnableIRQ
    empty.o(.text.main) refers to bsp_motor_hallencoder.o(.text.encoder_init) for encoder_init
    empty.o(.text.main) refers to hw_lcd.o(.text.lcd_init) for lcd_init
    empty.o(.text.main) refers to hardware_iic.o(.text.grayscale_detection_sensor) for grayscale_detection_sensor
    empty.o(.text.main) refers to empty.o(.text.PID_operation) for PID_operation
    empty.o(.text.main) refers to empty.o(.bss.left_speed_pid) for left_speed_pid
    empty.o(.text.main) refers to empty.o(.bss.right_speed_pid) for right_speed_pid
    empty.o(.text.main) refers to empty.o(.bss.posion_pid) for posion_pid
    empty.o(.text.main) refers to empty.o(.bss.posion_kalman) for posion_kalman
    empty.o(.text.main) refers to empty.o(.bss.left_kalman) for left_kalman
    empty.o(.text.main) refers to empty.o(.bss.right_kalman) for right_kalman
    empty.o(.text.main) refers to empty.o(.bss.PID_flash_time) for PID_flash_time
    empty.o(.ARM.exidx.text.main) refers to empty.o(.text.main) for [Anonymous Symbol]
    empty.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ) refers to empty.o(.text.__NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    empty.o(.ARM.exidx.text.__NVIC_EnableIRQ) refers to empty.o(.text.__NVIC_EnableIRQ) for [Anonymous Symbol]
    empty.o(.text.PID_operation) refers to sensor.o(.text.UpdatePosition) for UpdatePosition
    empty.o(.text.PID_operation) refers to bsp_motor_hallencoder.o(.text.get_encoder_count_L) for get_encoder_count_L
    empty.o(.text.PID_operation) refers to bsp_motor_hallencoder.o(.text.get_encoder_count_R) for get_encoder_count_R
    empty.o(.text.PID_operation) refers to fflti.o(.text) for __aeabi_i2f
    empty.o(.text.PID_operation) refers to filter.o(.text.Kalman_Update) for Kalman_Update
    empty.o(.text.PID_operation) refers to pid.o(.text.PID_Calculate) for PID_Calculate
    empty.o(.text.PID_operation) refers to fadd.o(.text) for __aeabi_fadd
    empty.o(.text.PID_operation) refers to ffixi.o(.text) for __aeabi_f2iz
    empty.o(.text.PID_operation) refers to bsp_tb6612.o(.text.AB_Control) for AB_Control
    empty.o(.text.PID_operation) refers to f2d.o(.text) for __aeabi_f2d
    empty.o(.text.PID_operation) refers to printfa.o(i.__0sprintf) for sprintf
    empty.o(.text.PID_operation) refers to usart.o(.text.uart_send) for uart_send
    empty.o(.text.PID_operation) refers to empty.o(.bss.left_kalman) for left_kalman
    empty.o(.text.PID_operation) refers to empty.o(.bss.right_kalman) for right_kalman
    empty.o(.text.PID_operation) refers to empty.o(.bss.posion_kalman) for posion_kalman
    empty.o(.text.PID_operation) refers to empty.o(.bss.posion_pid) for posion_pid
    empty.o(.text.PID_operation) refers to empty.o(.bss.PID_operation.total_speed) for [Anonymous Symbol]
    empty.o(.text.PID_operation) refers to empty.o(.bss.left_speed_pid) for left_speed_pid
    empty.o(.text.PID_operation) refers to empty.o(.bss.right_speed_pid) for right_speed_pid
    empty.o(.text.PID_operation) refers to empty.o(.bss.PID_operation.total_left_speed) for [Anonymous Symbol]
    empty.o(.text.PID_operation) refers to empty.o(.bss.PID_operation.total_right_speed) for [Anonymous Symbol]
    empty.o(.text.PID_operation) refers to hw_timer.o(.bss.Num_L) for Num_L
    empty.o(.text.PID_operation) refers to hw_timer.o(.bss.Num_R) for Num_R
    empty.o(.text.PID_operation) refers to usart.o(.bss.rx_buff) for rx_buff
    empty.o(.text.PID_operation) refers to empty.o(.rodata.str1.1) for [Anonymous Symbol]
    empty.o(.ARM.exidx.text.PID_operation) refers to empty.o(.text.PID_operation) for [Anonymous Symbol]
    empty.o(.text.TIMG0_IRQHandler) refers to empty.o(.text.DL_Timer_getPendingInterrupt) for DL_Timer_getPendingInterrupt
    empty.o(.text.TIMG0_IRQHandler) refers to empty.o(.bss.j) for j
    empty.o(.text.TIMG0_IRQHandler) refers to empty.o(.data.TIMG0_IRQHandler.timer_count) for [Anonymous Symbol]
    empty.o(.text.TIMG0_IRQHandler) refers to empty.o(.bss.led_flash_time) for led_flash_time
    empty.o(.text.TIMG0_IRQHandler) refers to empty.o(.bss.bmq_flash_time) for bmq_flash_time
    empty.o(.text.TIMG0_IRQHandler) refers to empty.o(.bss.PID_flash_time) for PID_flash_time
    empty.o(.ARM.exidx.text.TIMG0_IRQHandler) refers to empty.o(.text.TIMG0_IRQHandler) for [Anonymous Symbol]
    empty.o(.ARM.exidx.text.DL_Timer_getPendingInterrupt) refers to empty.o(.text.DL_Timer_getPendingInterrupt) for [Anonymous Symbol]
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(.text) for Reset_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to bsp_motor_hallencoder.o(.text.GROUP1_IRQHandler) for GROUP1_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to usart.o(.text.UART0_IRQHandler) for UART0_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to empty.o(.text.TIMG0_IRQHandler) for TIMG0_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to hw_timer.o(.text.TIMA0_IRQHandler) for TIMA0_IRQHandler
    startup_mspm0g350x_uvision.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for SYSCFG_DL_initPower
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for SYSCFG_DL_GPIO_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for SYSCFG_DL_SYSCTL_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) for SYSCFG_DL_PWM_LED_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) for SYSCFG_DL_PWM_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) for SYSCFG_DL_TIMER_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) for SYSCFG_DL_TIMER_TICK_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) for SYSCFG_DL_I2C_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for SYSCFG_DL_UART_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init) for SYSCFG_DL_SPI_LCD_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init) for SYSCFG_DL_DMA_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for SYSCFG_DL_SYSTICK_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gPWM_LEDBackup) for gPWM_LEDBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gPWM_0Backup) for gPWM_0Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gTIMER_TICKBackup) for gTIMER_TICKBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gSPI_LCDBackup) for gSPI_LCDBackup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_GPIO_reset) for DL_GPIO_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_Timer_reset) for DL_Timer_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_I2C_reset) for DL_I2C_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_UART_reset) for DL_UART_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_SPI_reset) for DL_SPI_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_GPIO_enablePower) for DL_GPIO_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_Timer_enablePower) for DL_Timer_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_I2C_enablePower) for DL_I2C_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_UART_enablePower) for DL_UART_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_SPI_enablePower) for DL_SPI_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralAnalogFunction) for DL_GPIO_initPeripheralAnalogFunction
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction) for DL_GPIO_initPeripheralOutputFunction
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunctionFeatures) for DL_GPIO_initPeripheralInputFunctionFeatures
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableHiZ) for DL_GPIO_enableHiZ
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction) for DL_GPIO_initPeripheralInputFunction
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutputFeatures) for DL_GPIO_initDigitalOutputFeatures
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures) for DL_GPIO_initDigitalInputFeatures
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_setLowerPinsPolarity) for DL_GPIO_setLowerPinsPolarity
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_setUpperPinsPolarity) for DL_GPIO_setUpperPinsPolarity
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus) for DL_GPIO_clearInterruptStatus
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt) for DL_GPIO_enableInterrupt
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold) for DL_SYSCTL_setBORThreshold
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState) for DL_SYSCTL_setFlashWaitState
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq) for DL_SYSCTL_setSYSOSCFreq
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT) for DL_SYSCTL_disableHFXT
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL) for DL_SYSCTL_disableSYSPLL
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams) for DL_SYSCTL_setHFCLKSourceHFXTParams
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL) for DL_SYSCTL_configSYSPLL
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider) for DL_SYSCTL_setULPCLKDivider
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFCLK) for DL_SYSCTL_enableMFCLK
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) for DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.__NVIC_SetPriority) for __NVIC_SetPriority
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.rodata.gSYSPLLConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to dl_timer.o(.text.DL_Timer_initFourCCPWMMode) for DL_Timer_initFourCCPWMMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to ti_msp_dl_config.o(.text.DL_Timer_setCounterControl) for DL_Timer_setCounterControl
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for DL_Timer_setCaptCompUpdateMethod
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableClock) for DL_Timer_enableClock
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection) for DL_Timer_setCCPDirection
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to ti_msp_dl_config.o(.rodata.gPWM_LEDClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to ti_msp_dl_config.o(.rodata.gPWM_LEDConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_LED_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_initFourCCPWMMode) for DL_Timer_initFourCCPWMMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.text.DL_Timer_setCounterControl) for DL_Timer_setCounterControl
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for DL_Timer_setCaptCompUpdateMethod
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableClock) for DL_Timer_enableClock
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection) for DL_Timer_setCCPDirection
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.rodata.gPWM_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.rodata.gPWM_0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for DL_Timer_initTimerMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt) for DL_Timer_enableInterrupt
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableClock) for DL_Timer_enableClock
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for DL_Timer_initTimerMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt) for DL_Timer_enableInterrupt
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) refers to ti_msp_dl_config.o(.text.__NVIC_SetPriority) for __NVIC_SetPriority
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableClock) for DL_Timer_enableClock
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_TICKClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_TICKTimerConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_TICK_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to dl_i2c.o(.text.DL_I2C_setClockConfig) for DL_I2C_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.DL_I2C_setAnalogGlitchFilterPulseWidth) for DL_I2C_setAnalogGlitchFilterPulseWidth
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.DL_I2C_enableAnalogGlitchFilter) for DL_I2C_enableAnalogGlitchFilter
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.DL_I2C_resetControllerTransfer) for DL_I2C_resetControllerTransfer
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.DL_I2C_setTimerPeriod) for DL_I2C_setTimerPeriod
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.DL_I2C_setControllerTXFIFOThreshold) for DL_I2C_setControllerTXFIFOThreshold
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.DL_I2C_setControllerRXFIFOThreshold) for DL_I2C_setControllerRXFIFOThreshold
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.DL_I2C_enableControllerClockStretching) for DL_I2C_enableControllerClockStretching
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.DL_I2C_enableController) for DL_I2C_enableController
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.rodata.gI2C_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_setOversampling) for DL_UART_setOversampling
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) for DL_UART_setBaudRateDivisor
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_enableInterrupt) for DL_UART_enableInterrupt
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.__NVIC_SetPriority) for __NVIC_SetPriority
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_enableFIFOs) for DL_UART_enableFIFOs
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_setRXFIFOThreshold) for DL_UART_setRXFIFOThreshold
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_setTXFIFOThreshold) for DL_UART_setTXFIFOThreshold
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_enable) for DL_UART_enable
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init) refers to dl_spi.o(.text.DL_SPI_setClockConfig) for DL_SPI_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init) refers to dl_spi.o(.text.DL_SPI_init) for DL_SPI_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init) refers to ti_msp_dl_config.o(.text.DL_SPI_setBitRateSerialClockDivider) for DL_SPI_setBitRateSerialClockDivider
    ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init) refers to ti_msp_dl_config.o(.text.DL_SPI_setFIFOThreshold) for DL_SPI_setFIFOThreshold
    ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init) refers to ti_msp_dl_config.o(.text.DL_SPI_enable) for DL_SPI_enable
    ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init) refers to ti_msp_dl_config.o(.rodata.gSPI_LCD_clockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init) refers to ti_msp_dl_config.o(.rodata.gSPI_LCD_config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SPI_LCD_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init) refers to ti_msp_dl_config.o(.text.DL_DMA_setBurstSize) for DL_DMA_setBurstSize
    ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init) for SYSCFG_DL_DMA_CH0_init
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DMA_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.DL_SYSTICK_init) for DL_SYSTICK_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.DL_SYSTICK_enable) for DL_SYSTICK_enable
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_timer.o(.text.DL_Timer_saveConfiguration) for DL_Timer_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_timer.o(.text.DL_TimerA_saveConfiguration) for DL_TimerA_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_spi.o(.text.DL_SPI_saveConfiguration) for DL_SPI_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_LEDBackup) for gPWM_LEDBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_0Backup) for gPWM_0Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gTIMER_TICKBackup) for gTIMER_TICKBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gSPI_LCDBackup) for gSPI_LCDBackup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_timer.o(.text.DL_Timer_restoreConfiguration) for DL_Timer_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_timer.o(.text.DL_TimerA_restoreConfiguration) for DL_TimerA_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_spi.o(.text.DL_SPI_restoreConfiguration) for DL_SPI_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_LEDBackup) for gPWM_LEDBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_0Backup) for gPWM_0Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gTIMER_TICKBackup) for gTIMER_TICKBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gSPI_LCDBackup) for gSPI_LCDBackup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_reset) refers to ti_msp_dl_config.o(.text.DL_GPIO_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_reset) refers to ti_msp_dl_config.o(.text.DL_Timer_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_reset) refers to ti_msp_dl_config.o(.text.DL_I2C_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_reset) refers to ti_msp_dl_config.o(.text.DL_UART_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SPI_reset) refers to ti_msp_dl_config.o(.text.DL_SPI_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enablePower) refers to ti_msp_dl_config.o(.text.DL_GPIO_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enablePower) refers to ti_msp_dl_config.o(.text.DL_Timer_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_enablePower) refers to ti_msp_dl_config.o(.text.DL_I2C_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enablePower) refers to ti_msp_dl_config.o(.text.DL_UART_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SPI_enablePower) refers to ti_msp_dl_config.o(.text.DL_SPI_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralAnalogFunction) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralAnalogFunction) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralOutputFunction) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableOutput) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableOutput) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralInputFunctionFeatures) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunctionFeatures) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableHiZ) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableHiZ) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralInputFunction) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalOutputFeatures) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutputFeatures) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalInputFeatures) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setPins) refers to ti_msp_dl_config.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setLowerPinsPolarity) refers to ti_msp_dl_config.o(.text.DL_GPIO_setLowerPinsPolarity) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setUpperPinsPolarity) refers to ti_msp_dl_config.o(.text.DL_GPIO_setUpperPinsPolarity) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearInterruptStatus) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableInterrupt) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setBORThreshold) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setFlashWaitState) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setSYSOSCFreq) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableHFXT) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableSYSPLL) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setULPCLKDivider) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_enableMFCLK) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFCLK) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.__NVIC_SetPriority) refers to ti_msp_dl_config.o(.text.__NVIC_SetPriority) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_Timer_setCounterControl) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_setCounterControl) refers to ti_msp_dl_config.o(.text.DL_Timer_setCounterControl) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enableClock) refers to ti_msp_dl_config.o(.text.DL_Timer_enableClock) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_setCCPDirection) refers to ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enableInterrupt) refers to ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_I2C_setAnalogGlitchFilterPulseWidth) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_setAnalogGlitchFilterPulseWidth) refers to ti_msp_dl_config.o(.text.DL_I2C_setAnalogGlitchFilterPulseWidth) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_enableAnalogGlitchFilter) refers to ti_msp_dl_config.o(.text.DL_I2C_enableAnalogGlitchFilter) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_resetControllerTransfer) refers to ti_msp_dl_config.o(.text.DL_I2C_resetControllerTransfer) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_setTimerPeriod) refers to ti_msp_dl_config.o(.text.DL_I2C_setTimerPeriod) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_I2C_setControllerTXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_setControllerTXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_I2C_setControllerTXFIFOThreshold) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_I2C_setControllerRXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_setControllerRXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_I2C_setControllerRXFIFOThreshold) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_enableControllerClockStretching) refers to ti_msp_dl_config.o(.text.DL_I2C_enableControllerClockStretching) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_enableController) refers to ti_msp_dl_config.o(.text.DL_I2C_enableController) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_UART_setOversampling) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setOversampling) refers to ti_msp_dl_config.o(.text.DL_UART_setOversampling) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setBaudRateDivisor) refers to ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableInterrupt) refers to ti_msp_dl_config.o(.text.DL_UART_enableInterrupt) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableFIFOs) refers to ti_msp_dl_config.o(.text.DL_UART_enableFIFOs) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_UART_setRXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setRXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_UART_setRXFIFOThreshold) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_UART_setTXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setTXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_UART_setTXFIFOThreshold) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enable) refers to ti_msp_dl_config.o(.text.DL_UART_enable) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_SPI_setBitRateSerialClockDivider) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SPI_setBitRateSerialClockDivider) refers to ti_msp_dl_config.o(.text.DL_SPI_setBitRateSerialClockDivider) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_SPI_setFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SPI_setFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_SPI_setFIFOThreshold) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SPI_enable) refers to ti_msp_dl_config.o(.text.DL_SPI_enable) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init) refers to ti_msp_dl_config.o(.text.DL_DMA_clearInterruptStatus) for DL_DMA_clearInterruptStatus
    ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init) refers to ti_msp_dl_config.o(.text.DL_DMA_enableInterrupt) for DL_DMA_enableInterrupt
    ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init) refers to dl_dma.o(.text.DL_DMA_initChannel) for DL_DMA_initChannel
    ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init) refers to ti_msp_dl_config.o(.rodata.gDMA_CH0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DMA_CH0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_DMA_clearInterruptStatus) refers to ti_msp_dl_config.o(.text.DL_DMA_clearInterruptStatus) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_DMA_enableInterrupt) refers to ti_msp_dl_config.o(.text.DL_DMA_enableInterrupt) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_DMA_setBurstSize) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_DMA_setBurstSize) refers to ti_msp_dl_config.o(.text.DL_DMA_setBurstSize) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.DL_SYSTICK_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSTICK_enable) refers to ti_msp_dl_config.o(.text.DL_SYSTICK_enable) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Common_updateReg) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    led.o(.text.LED_flash) refers to led.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    led.o(.text.LED_flash) refers to time.o(.text.delay_ms) for delay_ms
    led.o(.text.LED_flash) refers to led.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    led.o(.ARM.exidx.text.LED_flash) refers to led.o(.text.LED_flash) for [Anonymous Symbol]
    led.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to led.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    led.o(.ARM.exidx.text.DL_GPIO_setPins) refers to led.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    key.o(.text.KEY_control_LED) refers to key.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    key.o(.text.KEY_control_LED) refers to key.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    key.o(.text.KEY_control_LED) refers to key.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    key.o(.ARM.exidx.text.KEY_control_LED) refers to key.o(.text.KEY_control_LED) for [Anonymous Symbol]
    key.o(.ARM.exidx.text.DL_GPIO_readPins) refers to key.o(.text.DL_GPIO_readPins) for [Anonymous Symbol]
    key.o(.ARM.exidx.text.DL_GPIO_setPins) refers to key.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    key.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to key.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    key.o(.text.Get_KEY) refers to key.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    key.o(.text.Get_KEY) refers to time.o(.text.delay_ms) for delay_ms
    key.o(.ARM.exidx.text.Get_KEY) refers to key.o(.text.Get_KEY) for [Anonymous Symbol]
    usart.o(.text.uart0_send_char) refers to usart.o(.text.DL_UART_isBusy) for DL_UART_isBusy
    usart.o(.text.uart0_send_char) refers to usart.o(.text.DL_UART_transmitData) for DL_UART_transmitData
    usart.o(.ARM.exidx.text.uart0_send_char) refers to usart.o(.text.uart0_send_char) for [Anonymous Symbol]
    usart.o(.ARM.exidx.text.DL_UART_isBusy) refers to usart.o(.text.DL_UART_isBusy) for [Anonymous Symbol]
    usart.o(.ARM.exidx.text.DL_UART_transmitData) refers to usart.o(.text.DL_UART_transmitData) for [Anonymous Symbol]
    usart.o(.text.uart0_send_string) refers to usart.o(.text.uart0_send_char) for uart0_send_char
    usart.o(.ARM.exidx.text.uart0_send_string) refers to usart.o(.text.uart0_send_string) for [Anonymous Symbol]
    usart.o(.text.fputc) refers to usart.o(.text.DL_UART_isBusy) for DL_UART_isBusy
    usart.o(.text.fputc) refers to usart.o(.text.DL_UART_transmitData) for DL_UART_transmitData
    usart.o(.ARM.exidx.text.fputc) refers to usart.o(.text.fputc) for [Anonymous Symbol]
    usart.o(.text.uart_send) refers to strlen.o(.text) for strlen
    usart.o(.text.uart_send) refers to dl_uart.o(.text.DL_UART_transmitDataCheck) for DL_UART_transmitDataCheck
    usart.o(.text.uart_send) refers to usart.o(.data.uart_tx_dma_complete_flag) for uart_tx_dma_complete_flag
    usart.o(.text.uart_send) refers to usart.o(.bss.send_len) for send_len
    usart.o(.text.uart_send) refers to usart.o(.bss.has_send_len) for has_send_len
    usart.o(.ARM.exidx.text.uart_send) refers to usart.o(.text.uart_send) for [Anonymous Symbol]
    usart.o(.text.UART0_IRQHandler) refers to usart.o(.text.DL_UART_getPendingInterrupt) for DL_UART_getPendingInterrupt
    usart.o(.text.UART0_IRQHandler) refers to usart.o(.text.uart_send) for uart_send
    usart.o(.text.UART0_IRQHandler) refers to usart.o(.data.uart_tx_dma_complete_flag) for uart_tx_dma_complete_flag
    usart.o(.text.UART0_IRQHandler) refers to usart.o(.bss.rx_buff) for rx_buff
    usart.o(.ARM.exidx.text.UART0_IRQHandler) refers to usart.o(.text.UART0_IRQHandler) for [Anonymous Symbol]
    usart.o(.ARM.exidx.text.DL_UART_getPendingInterrupt) refers to usart.o(.text.DL_UART_getPendingInterrupt) for [Anonymous Symbol]
    pwm.o(.text.PWM_LED) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    pwm.o(.text.PWM_LED) refers to time.o(.text.delay_ms) for delay_ms
    pwm.o(.ARM.exidx.text.PWM_LED) refers to pwm.o(.text.PWM_LED) for [Anonymous Symbol]
    iic.o(.text.IIC_Start) refers to iic.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    iic.o(.text.IIC_Start) refers to iic.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    iic.o(.text.IIC_Start) refers to iic.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    iic.o(.text.IIC_Start) refers to iic.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    iic.o(.text.IIC_Start) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    iic.o(.ARM.exidx.text.IIC_Start) refers to iic.o(.text.IIC_Start) for [Anonymous Symbol]
    iic.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput) refers to iic.o(.text.DL_GPIO_initDigitalOutput) for [Anonymous Symbol]
    iic.o(.ARM.exidx.text.DL_GPIO_setPins) refers to iic.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    iic.o(.ARM.exidx.text.DL_GPIO_enableOutput) refers to iic.o(.text.DL_GPIO_enableOutput) for [Anonymous Symbol]
    iic.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to iic.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    iic.o(.text.IIC_Stop) refers to iic.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    iic.o(.text.IIC_Stop) refers to iic.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    iic.o(.text.IIC_Stop) refers to iic.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    iic.o(.text.IIC_Stop) refers to iic.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    iic.o(.text.IIC_Stop) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    iic.o(.ARM.exidx.text.IIC_Stop) refers to iic.o(.text.IIC_Stop) for [Anonymous Symbol]
    iic.o(.text.IIC_Send_Ack) refers to iic.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    iic.o(.text.IIC_Send_Ack) refers to iic.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    iic.o(.text.IIC_Send_Ack) refers to iic.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    iic.o(.text.IIC_Send_Ack) refers to iic.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    iic.o(.text.IIC_Send_Ack) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    iic.o(.ARM.exidx.text.IIC_Send_Ack) refers to iic.o(.text.IIC_Send_Ack) for [Anonymous Symbol]
    iic.o(.text.IIC_Wait_Ack) refers to iic.o(.text.DL_GPIO_initDigitalInput) for DL_GPIO_initDigitalInput
    iic.o(.text.IIC_Wait_Ack) refers to iic.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    iic.o(.text.IIC_Wait_Ack) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    iic.o(.text.IIC_Wait_Ack) refers to iic.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    iic.o(.text.IIC_Wait_Ack) refers to iic.o(.text.IIC_Stop) for IIC_Stop
    iic.o(.text.IIC_Wait_Ack) refers to iic.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    iic.o(.text.IIC_Wait_Ack) refers to iic.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    iic.o(.text.IIC_Wait_Ack) refers to iic.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    iic.o(.ARM.exidx.text.IIC_Wait_Ack) refers to iic.o(.text.IIC_Wait_Ack) for [Anonymous Symbol]
    iic.o(.ARM.exidx.text.DL_GPIO_initDigitalInput) refers to iic.o(.text.DL_GPIO_initDigitalInput) for [Anonymous Symbol]
    iic.o(.ARM.exidx.text.DL_GPIO_readPins) refers to iic.o(.text.DL_GPIO_readPins) for [Anonymous Symbol]
    iic.o(.text.IIC_Send_Byte) refers to iic.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    iic.o(.text.IIC_Send_Byte) refers to iic.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    iic.o(.text.IIC_Send_Byte) refers to iic.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    iic.o(.text.IIC_Send_Byte) refers to iic.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    iic.o(.text.IIC_Send_Byte) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    iic.o(.ARM.exidx.text.IIC_Send_Byte) refers to iic.o(.text.IIC_Send_Byte) for [Anonymous Symbol]
    iic.o(.text.IIC_Read_Byte) refers to iic.o(.text.DL_GPIO_initDigitalInput) for DL_GPIO_initDigitalInput
    iic.o(.text.IIC_Read_Byte) refers to iic.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    iic.o(.text.IIC_Read_Byte) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    iic.o(.text.IIC_Read_Byte) refers to iic.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    iic.o(.text.IIC_Read_Byte) refers to iic.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    iic.o(.ARM.exidx.text.IIC_Read_Byte) refers to iic.o(.text.IIC_Read_Byte) for [Anonymous Symbol]
    iic.o(.text.SHT20_Read) refers to iic.o(.text.IIC_Start) for IIC_Start
    iic.o(.text.SHT20_Read) refers to iic.o(.text.IIC_Send_Byte) for IIC_Send_Byte
    iic.o(.text.SHT20_Read) refers to iic.o(.text.IIC_Wait_Ack) for IIC_Wait_Ack
    iic.o(.text.SHT20_Read) refers to printfa.o(i.__0printf) for printf
    iic.o(.text.SHT20_Read) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    iic.o(.text.SHT20_Read) refers to iic.o(.text.IIC_Read_Byte) for IIC_Read_Byte
    iic.o(.text.SHT20_Read) refers to iic.o(.text.IIC_Send_Ack) for IIC_Send_Ack
    iic.o(.text.SHT20_Read) refers to iic.o(.text.IIC_Stop) for IIC_Stop
    iic.o(.text.SHT20_Read) refers to dflti.o(.text) for __aeabi_i2d
    iic.o(.text.SHT20_Read) refers to ddiv.o(.text) for __aeabi_ddiv
    iic.o(.text.SHT20_Read) refers to dmul.o(.text) for __aeabi_dmul
    iic.o(.text.SHT20_Read) refers to dadd.o(.text) for __aeabi_dadd
    iic.o(.text.SHT20_Read) refers to d2f.o(.text) for __aeabi_d2f
    iic.o(.text.SHT20_Read) refers to iic.o(.rodata.str1.1) for [Anonymous Symbol]
    iic.o(.ARM.exidx.text.SHT20_Read) refers to iic.o(.text.SHT20_Read) for [Anonymous Symbol]
    hw_lcd.o(.text.spi_write_bus) refers to hw_lcd.o(.text.DL_SPI_transmitData8) for DL_SPI_transmitData8
    hw_lcd.o(.text.spi_write_bus) refers to hw_lcd.o(.text.DL_SPI_isBusy) for DL_SPI_isBusy
    hw_lcd.o(.ARM.exidx.text.spi_write_bus) refers to hw_lcd.o(.text.spi_write_bus) for [Anonymous Symbol]
    hw_lcd.o(.ARM.exidx.text.DL_SPI_transmitData8) refers to hw_lcd.o(.text.DL_SPI_transmitData8) for [Anonymous Symbol]
    hw_lcd.o(.ARM.exidx.text.DL_SPI_isBusy) refers to hw_lcd.o(.text.DL_SPI_isBusy) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_Writ_Bus) refers to hw_lcd.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    hw_lcd.o(.text.LCD_Writ_Bus) refers to hw_lcd.o(.text.spi_write_bus) for spi_write_bus
    hw_lcd.o(.text.LCD_Writ_Bus) refers to hw_lcd.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    hw_lcd.o(.ARM.exidx.text.LCD_Writ_Bus) refers to hw_lcd.o(.text.LCD_Writ_Bus) for [Anonymous Symbol]
    hw_lcd.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to hw_lcd.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    hw_lcd.o(.ARM.exidx.text.DL_GPIO_setPins) refers to hw_lcd.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_WR_DATA8) refers to hw_lcd.o(.text.LCD_Writ_Bus) for LCD_Writ_Bus
    hw_lcd.o(.ARM.exidx.text.LCD_WR_DATA8) refers to hw_lcd.o(.text.LCD_WR_DATA8) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_WR_DATA) refers to hw_lcd.o(.text.LCD_Writ_Bus) for LCD_Writ_Bus
    hw_lcd.o(.ARM.exidx.text.LCD_WR_DATA) refers to hw_lcd.o(.text.LCD_WR_DATA) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_WR_REG) refers to hw_lcd.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    hw_lcd.o(.text.LCD_WR_REG) refers to hw_lcd.o(.text.LCD_Writ_Bus) for LCD_Writ_Bus
    hw_lcd.o(.text.LCD_WR_REG) refers to hw_lcd.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    hw_lcd.o(.ARM.exidx.text.LCD_WR_REG) refers to hw_lcd.o(.text.LCD_WR_REG) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_Address_Set) refers to hw_lcd.o(.text.LCD_WR_REG) for LCD_WR_REG
    hw_lcd.o(.text.LCD_Address_Set) refers to hw_lcd.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    hw_lcd.o(.ARM.exidx.text.LCD_Address_Set) refers to hw_lcd.o(.text.LCD_Address_Set) for [Anonymous Symbol]
    hw_lcd.o(.text.lcd_init) refers to hw_lcd.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    hw_lcd.o(.text.lcd_init) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    hw_lcd.o(.text.lcd_init) refers to hw_lcd.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    hw_lcd.o(.text.lcd_init) refers to hw_lcd.o(.text.LCD_WR_REG) for LCD_WR_REG
    hw_lcd.o(.text.lcd_init) refers to hw_lcd.o(.text.LCD_WR_DATA8) for LCD_WR_DATA8
    hw_lcd.o(.ARM.exidx.text.lcd_init) refers to hw_lcd.o(.text.lcd_init) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_Fill) refers to hw_lcd.o(.text.LCD_Address_Set) for LCD_Address_Set
    hw_lcd.o(.text.LCD_Fill) refers to hw_lcd.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    hw_lcd.o(.ARM.exidx.text.LCD_Fill) refers to hw_lcd.o(.text.LCD_Fill) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_DrawPoint) refers to hw_lcd.o(.text.LCD_Address_Set) for LCD_Address_Set
    hw_lcd.o(.text.LCD_DrawPoint) refers to hw_lcd.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    hw_lcd.o(.ARM.exidx.text.LCD_DrawPoint) refers to hw_lcd.o(.text.LCD_DrawPoint) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_DrawLine) refers to hw_lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    hw_lcd.o(.ARM.exidx.text.LCD_DrawLine) refers to hw_lcd.o(.text.LCD_DrawLine) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_DrawVerrticalLine) refers to hw_lcd.o(.text.LCD_Address_Set) for LCD_Address_Set
    hw_lcd.o(.text.LCD_DrawVerrticalLine) refers to hw_lcd.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    hw_lcd.o(.ARM.exidx.text.LCD_DrawVerrticalLine) refers to hw_lcd.o(.text.LCD_DrawVerrticalLine) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_DrawRectangle) refers to hw_lcd.o(.text.LCD_DrawLine) for LCD_DrawLine
    hw_lcd.o(.ARM.exidx.text.LCD_DrawRectangle) refers to hw_lcd.o(.text.LCD_DrawRectangle) for [Anonymous Symbol]
    hw_lcd.o(.text.Draw_Circle) refers to hw_lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    hw_lcd.o(.ARM.exidx.text.Draw_Circle) refers to hw_lcd.o(.text.Draw_Circle) for [Anonymous Symbol]
    hw_lcd.o(.text.Drawarc) refers to dfltui.o(.text) for __aeabi_ui2d
    hw_lcd.o(.text.Drawarc) refers to sqrt.o(i.sqrt) for sqrt
    hw_lcd.o(.text.Drawarc) refers to dfixi.o(.text) for __aeabi_d2iz
    hw_lcd.o(.text.Drawarc) refers to hw_lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    hw_lcd.o(.ARM.exidx.text.Drawarc) refers to hw_lcd.o(.text.Drawarc) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ArcRect) refers to hw_lcd.o(.text.LCD_DrawLine) for LCD_DrawLine
    hw_lcd.o(.text.LCD_ArcRect) refers to hw_lcd.o(.text.Drawarc) for Drawarc
    hw_lcd.o(.text.LCD_ArcRect) refers to hw_lcd.o(.text.LCD_Fill) for LCD_Fill
    hw_lcd.o(.ARM.exidx.text.LCD_ArcRect) refers to hw_lcd.o(.text.LCD_ArcRect) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ShowChinese) refers to hw_lcd.o(.text.LCD_ShowChinese12x12) for LCD_ShowChinese12x12
    hw_lcd.o(.text.LCD_ShowChinese) refers to hw_lcd.o(.text.LCD_ShowChinese16x16) for LCD_ShowChinese16x16
    hw_lcd.o(.text.LCD_ShowChinese) refers to hw_lcd.o(.text.LCD_ShowChinese24x24) for LCD_ShowChinese24x24
    hw_lcd.o(.text.LCD_ShowChinese) refers to hw_lcd.o(.text.LCD_ShowChinese32x32) for LCD_ShowChinese32x32
    hw_lcd.o(.text.LCD_ShowChinese) refers to hw_lcd.o(.text.LCD_ShowChar) for LCD_ShowChar
    hw_lcd.o(.ARM.exidx.text.LCD_ShowChinese) refers to hw_lcd.o(.text.LCD_ShowChinese) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ShowChinese12x12) refers to hw_lcd.o(.text.LCD_Address_Set) for LCD_Address_Set
    hw_lcd.o(.text.LCD_ShowChinese12x12) refers to hw_lcd.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    hw_lcd.o(.text.LCD_ShowChinese12x12) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    hw_lcd.o(.text.LCD_ShowChinese12x12) refers to hw_lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    hw_lcd.o(.text.LCD_ShowChinese12x12) refers to hw_lcd.o(.rodata.tfont12) for tfont12
    hw_lcd.o(.ARM.exidx.text.LCD_ShowChinese12x12) refers to hw_lcd.o(.text.LCD_ShowChinese12x12) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ShowChinese16x16) refers to hw_lcd.o(.text.LCD_Address_Set) for LCD_Address_Set
    hw_lcd.o(.text.LCD_ShowChinese16x16) refers to hw_lcd.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    hw_lcd.o(.text.LCD_ShowChinese16x16) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    hw_lcd.o(.text.LCD_ShowChinese16x16) refers to hw_lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    hw_lcd.o(.text.LCD_ShowChinese16x16) refers to hw_lcd.o(.rodata.tfont16) for tfont16
    hw_lcd.o(.ARM.exidx.text.LCD_ShowChinese16x16) refers to hw_lcd.o(.text.LCD_ShowChinese16x16) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ShowChinese24x24) refers to hw_lcd.o(.text.LCD_Address_Set) for LCD_Address_Set
    hw_lcd.o(.text.LCD_ShowChinese24x24) refers to hw_lcd.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    hw_lcd.o(.text.LCD_ShowChinese24x24) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    hw_lcd.o(.text.LCD_ShowChinese24x24) refers to hw_lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    hw_lcd.o(.text.LCD_ShowChinese24x24) refers to hw_lcd.o(.rodata.tfont24) for tfont24
    hw_lcd.o(.ARM.exidx.text.LCD_ShowChinese24x24) refers to hw_lcd.o(.text.LCD_ShowChinese24x24) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ShowChinese32x32) refers to hw_lcd.o(.text.LCD_Address_Set) for LCD_Address_Set
    hw_lcd.o(.text.LCD_ShowChinese32x32) refers to hw_lcd.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    hw_lcd.o(.text.LCD_ShowChinese32x32) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    hw_lcd.o(.text.LCD_ShowChinese32x32) refers to hw_lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    hw_lcd.o(.text.LCD_ShowChinese32x32) refers to hw_lcd.o(.rodata.tfont32) for tfont32
    hw_lcd.o(.ARM.exidx.text.LCD_ShowChinese32x32) refers to hw_lcd.o(.text.LCD_ShowChinese32x32) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ShowChar) refers to hw_lcd.o(.text.LCD_Address_Set) for LCD_Address_Set
    hw_lcd.o(.text.LCD_ShowChar) refers to hw_lcd.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    hw_lcd.o(.text.LCD_ShowChar) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    hw_lcd.o(.text.LCD_ShowChar) refers to hw_lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    hw_lcd.o(.text.LCD_ShowChar) refers to hw_lcd.o(.rodata.ascii_3216) for ascii_3216
    hw_lcd.o(.text.LCD_ShowChar) refers to hw_lcd.o(.rodata.ascii_2412) for ascii_2412
    hw_lcd.o(.text.LCD_ShowChar) refers to hw_lcd.o(.rodata.ascii_1608) for ascii_1608
    hw_lcd.o(.text.LCD_ShowChar) refers to hw_lcd.o(.rodata.ascii_1206) for ascii_1206
    hw_lcd.o(.ARM.exidx.text.LCD_ShowChar) refers to hw_lcd.o(.text.LCD_ShowChar) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ShowString) refers to hw_lcd.o(.text.LCD_ShowChar) for LCD_ShowChar
    hw_lcd.o(.ARM.exidx.text.LCD_ShowString) refers to hw_lcd.o(.text.LCD_ShowString) for [Anonymous Symbol]
    hw_lcd.o(.ARM.exidx.text.mypow) refers to hw_lcd.o(.text.mypow) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ShowIntNum) refers to hw_lcd.o(.text.mypow) for mypow
    hw_lcd.o(.text.LCD_ShowIntNum) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    hw_lcd.o(.text.LCD_ShowIntNum) refers to hw_lcd.o(.text.LCD_ShowChar) for LCD_ShowChar
    hw_lcd.o(.ARM.exidx.text.LCD_ShowIntNum) refers to hw_lcd.o(.text.LCD_ShowIntNum) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ShowFloatNum1) refers to fmul.o(.text) for __aeabi_fmul
    hw_lcd.o(.text.LCD_ShowFloatNum1) refers to ffixui.o(.text) for __aeabi_f2uiz
    hw_lcd.o(.text.LCD_ShowFloatNum1) refers to hw_lcd.o(.text.mypow) for mypow
    hw_lcd.o(.text.LCD_ShowFloatNum1) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    hw_lcd.o(.text.LCD_ShowFloatNum1) refers to hw_lcd.o(.text.LCD_ShowChar) for LCD_ShowChar
    hw_lcd.o(.ARM.exidx.text.LCD_ShowFloatNum1) refers to hw_lcd.o(.text.LCD_ShowFloatNum1) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ShowPicture) refers to hw_lcd.o(.text.LCD_Address_Set) for LCD_Address_Set
    hw_lcd.o(.text.LCD_ShowPicture) refers to hw_lcd.o(.text.LCD_WR_DATA8) for LCD_WR_DATA8
    hw_lcd.o(.ARM.exidx.text.LCD_ShowPicture) refers to hw_lcd.o(.text.LCD_ShowPicture) for [Anonymous Symbol]
    oled.o(.text.disp_x_center) refers to hw_lcd.o(.text.LCD_ArcRect) for LCD_ArcRect
    oled.o(.text.disp_x_center) refers to hw_lcd.o(.text.LCD_ShowChinese) for LCD_ShowChinese
    oled.o(.ARM.exidx.text.disp_x_center) refers to oled.o(.text.disp_x_center) for [Anonymous Symbol]
    oled.o(.text.disp_string_rect) refers to hw_lcd.o(.text.LCD_ArcRect) for LCD_ArcRect
    oled.o(.text.disp_string_rect) refers to hw_lcd.o(.text.LCD_ShowChinese) for LCD_ShowChinese
    oled.o(.ARM.exidx.text.disp_string_rect) refers to oled.o(.text.disp_string_rect) for [Anonymous Symbol]
    oled.o(.text.disp_select_box) refers to hw_lcd.o(.text.LCD_DrawLine) for LCD_DrawLine
    oled.o(.ARM.exidx.text.disp_select_box) refers to oled.o(.text.disp_select_box) for [Anonymous Symbol]
    oled.o(.text.ui_home_page) refers to oled.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    oled.o(.text.ui_home_page) refers to hw_lcd.o(.text.LCD_Fill) for LCD_Fill
    oled.o(.text.ui_home_page) refers to oled.o(.text.disp_x_center) for disp_x_center
    oled.o(.text.ui_home_page) refers to oled.o(.text.disp_string_rect) for disp_string_rect
    oled.o(.text.ui_home_page) refers to oled.o(.text.disp_select_box) for disp_select_box
    oled.o(.text.ui_home_page) refers to oled.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    oled.o(.text.ui_home_page) refers to oled.o(.rodata.str1.1) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.ui_home_page) refers to oled.o(.text.ui_home_page) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to oled.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.DL_GPIO_setPins) refers to oled.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    hw_key.o(.text.key_scan) refers to hw_key.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    hw_key.o(.ARM.exidx.text.key_scan) refers to hw_key.o(.text.key_scan) for [Anonymous Symbol]
    hw_key.o(.ARM.exidx.text.DL_GPIO_readPins) refers to hw_key.o(.text.DL_GPIO_readPins) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.PID_Init) refers to pid.o(.text.PID_Init) for [Anonymous Symbol]
    pid.o(.text.PID_Calculate) refers to fadd.o(.text) for __aeabi_fsub
    pid.o(.text.PID_Calculate) refers to fcmple.o(.text) for __aeabi_fcmple
    pid.o(.text.PID_Calculate) refers to fmul.o(.text) for __aeabi_fmul
    pid.o(.text.PID_Calculate) refers to fdiv.o(.text) for __aeabi_fdiv
    pid.o(.ARM.exidx.text.PID_Calculate) refers to pid.o(.text.PID_Calculate) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.text.encoder_init) refers to bsp_motor_hallencoder.o(.text.__NVIC_ClearPendingIRQ) for __NVIC_ClearPendingIRQ
    bsp_motor_hallencoder.o(.text.encoder_init) refers to bsp_motor_hallencoder.o(.text.__NVIC_EnableIRQ) for __NVIC_EnableIRQ
    bsp_motor_hallencoder.o(.ARM.exidx.text.encoder_init) refers to bsp_motor_hallencoder.o(.text.encoder_init) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ) refers to bsp_motor_hallencoder.o(.text.__NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.ARM.exidx.text.__NVIC_EnableIRQ) refers to bsp_motor_hallencoder.o(.text.__NVIC_EnableIRQ) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.text.get_encoder_count_L) refers to dflti.o(.text) for __aeabi_i2d
    bsp_motor_hallencoder.o(.text.get_encoder_count_L) refers to dmul.o(.text) for __aeabi_dmul
    bsp_motor_hallencoder.o(.text.get_encoder_count_L) refers to dfixi.o(.text) for __aeabi_d2iz
    bsp_motor_hallencoder.o(.text.get_encoder_count_L) refers to bsp_motor_hallencoder.o(.bss.motor_encoder_L) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.ARM.exidx.text.get_encoder_count_L) refers to bsp_motor_hallencoder.o(.text.get_encoder_count_L) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.text.get_encoder_count_R) refers to dflti.o(.text) for __aeabi_i2d
    bsp_motor_hallencoder.o(.text.get_encoder_count_R) refers to dmul.o(.text) for __aeabi_dmul
    bsp_motor_hallencoder.o(.text.get_encoder_count_R) refers to dfixi.o(.text) for __aeabi_d2iz
    bsp_motor_hallencoder.o(.text.get_encoder_count_R) refers to bsp_motor_hallencoder.o(.bss.motor_encoder_R) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.ARM.exidx.text.get_encoder_count_R) refers to bsp_motor_hallencoder.o(.text.get_encoder_count_R) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.text.get_encoder_dir_L) refers to bsp_motor_hallencoder.o(.bss.motor_encoder_L) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.ARM.exidx.text.get_encoder_dir_L) refers to bsp_motor_hallencoder.o(.text.get_encoder_dir_L) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.text.get_encoder_dir_R) refers to bsp_motor_hallencoder.o(.bss.motor_encoder_R) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.ARM.exidx.text.get_encoder_dir_R) refers to bsp_motor_hallencoder.o(.text.get_encoder_dir_R) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.text.encoder_update_L) refers to bsp_motor_hallencoder.o(.bss.motor_encoder_L) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.ARM.exidx.text.encoder_update_L) refers to bsp_motor_hallencoder.o(.text.encoder_update_L) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.text.encoder_update_R) refers to bsp_motor_hallencoder.o(.bss.motor_encoder_R) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.ARM.exidx.text.encoder_update_R) refers to bsp_motor_hallencoder.o(.text.encoder_update_R) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.text.GROUP1_IRQHandler) refers to bsp_motor_hallencoder.o(.text.DL_GPIO_getEnabledInterruptStatus) for DL_GPIO_getEnabledInterruptStatus
    bsp_motor_hallencoder.o(.text.GROUP1_IRQHandler) refers to bsp_motor_hallencoder.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    bsp_motor_hallencoder.o(.text.GROUP1_IRQHandler) refers to bsp_motor_hallencoder.o(.text.DL_GPIO_clearInterruptStatus) for DL_GPIO_clearInterruptStatus
    bsp_motor_hallencoder.o(.text.GROUP1_IRQHandler) refers to bsp_motor_hallencoder.o(.bss.motor_encoder_R) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.text.GROUP1_IRQHandler) refers to bsp_motor_hallencoder.o(.bss.motor_encoder_L) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.ARM.exidx.text.GROUP1_IRQHandler) refers to bsp_motor_hallencoder.o(.text.GROUP1_IRQHandler) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.ARM.exidx.text.DL_GPIO_getEnabledInterruptStatus) refers to bsp_motor_hallencoder.o(.text.DL_GPIO_getEnabledInterruptStatus) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.ARM.exidx.text.DL_GPIO_readPins) refers to bsp_motor_hallencoder.o(.text.DL_GPIO_readPins) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.ARM.exidx.text.DL_GPIO_clearInterruptStatus) refers to bsp_motor_hallencoder.o(.text.DL_GPIO_clearInterruptStatus) for [Anonymous Symbol]
    bsp_tb6612.o(.text.TB6612_Motor_Stop) refers to bsp_tb6612.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    bsp_tb6612.o(.ARM.exidx.text.TB6612_Motor_Stop) refers to bsp_tb6612.o(.text.TB6612_Motor_Stop) for [Anonymous Symbol]
    bsp_tb6612.o(.ARM.exidx.text.DL_GPIO_setPins) refers to bsp_tb6612.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    bsp_tb6612.o(.text.AB_Control) refers to bsp_tb6612.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    bsp_tb6612.o(.text.AB_Control) refers to bsp_tb6612.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    bsp_tb6612.o(.text.AB_Control) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    bsp_tb6612.o(.ARM.exidx.text.AB_Control) refers to bsp_tb6612.o(.text.AB_Control) for [Anonymous Symbol]
    bsp_tb6612.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to bsp_tb6612.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    hw_timer.o(.text.timer_init) refers to hw_timer.o(.text.__NVIC_ClearPendingIRQ) for __NVIC_ClearPendingIRQ
    hw_timer.o(.text.timer_init) refers to hw_timer.o(.text.__NVIC_EnableIRQ) for __NVIC_EnableIRQ
    hw_timer.o(.ARM.exidx.text.timer_init) refers to hw_timer.o(.text.timer_init) for [Anonymous Symbol]
    hw_timer.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ) refers to hw_timer.o(.text.__NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    hw_timer.o(.ARM.exidx.text.__NVIC_EnableIRQ) refers to hw_timer.o(.text.__NVIC_EnableIRQ) for [Anonymous Symbol]
    hw_timer.o(.text.TIMA0_IRQHandler) refers to hw_timer.o(.text.DL_Timer_getPendingInterrupt) for DL_Timer_getPendingInterrupt
    hw_timer.o(.text.TIMA0_IRQHandler) refers to bsp_motor_hallencoder.o(.text.encoder_update_L) for encoder_update_L
    hw_timer.o(.text.TIMA0_IRQHandler) refers to bsp_motor_hallencoder.o(.text.encoder_update_R) for encoder_update_R
    hw_timer.o(.ARM.exidx.text.TIMA0_IRQHandler) refers to hw_timer.o(.text.TIMA0_IRQHandler) for [Anonymous Symbol]
    hw_timer.o(.ARM.exidx.text.DL_Timer_getPendingInterrupt) refers to hw_timer.o(.text.DL_Timer_getPendingInterrupt) for [Anonymous Symbol]
    filter.o(.ARM.exidx.text.Kalman_Init) refers to filter.o(.text.Kalman_Init) for [Anonymous Symbol]
    filter.o(.text.Kalman_Update) refers to fadd.o(.text) for __aeabi_fadd
    filter.o(.text.Kalman_Update) refers to fdiv.o(.text) for __aeabi_fdiv
    filter.o(.text.Kalman_Update) refers to fmul.o(.text) for __aeabi_fmul
    filter.o(.ARM.exidx.text.Kalman_Update) refers to filter.o(.text.Kalman_Update) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_ReadByte) refers to hw_i2c.o(.text.hardware_IIC_ReadByte) for hardware_IIC_ReadByte
    hardware_iic.o(.ARM.exidx.text.IIC_ReadByte) refers to hardware_iic.o(.text.IIC_ReadByte) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_ReadBytes) refers to hw_i2c.o(.text.hardware_IIC_ReadBytes) for hardware_IIC_ReadBytes
    hardware_iic.o(.ARM.exidx.text.IIC_ReadBytes) refers to hardware_iic.o(.text.IIC_ReadBytes) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_WriteByte) refers to hw_i2c.o(.text.hardware_IIC_WirteByte) for hardware_IIC_WirteByte
    hardware_iic.o(.ARM.exidx.text.IIC_WriteByte) refers to hardware_iic.o(.text.IIC_WriteByte) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_WriteBytes) refers to hw_i2c.o(.text.hardware_IIC_WirteBytes) for hardware_IIC_WirteBytes
    hardware_iic.o(.ARM.exidx.text.IIC_WriteBytes) refers to hardware_iic.o(.text.IIC_WriteBytes) for [Anonymous Symbol]
    hardware_iic.o(.text.Ping) refers to hardware_iic.o(.text.IIC_ReadByte) for IIC_ReadByte
    hardware_iic.o(.ARM.exidx.text.Ping) refers to hardware_iic.o(.text.Ping) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_Get_Digtal) refers to hardware_iic.o(.text.IIC_ReadByte) for IIC_ReadByte
    hardware_iic.o(.ARM.exidx.text.IIC_Get_Digtal) refers to hardware_iic.o(.text.IIC_Get_Digtal) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_Get_Anolog) refers to hardware_iic.o(.text.IIC_ReadBytes) for IIC_ReadBytes
    hardware_iic.o(.ARM.exidx.text.IIC_Get_Anolog) refers to hardware_iic.o(.text.IIC_Get_Anolog) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_Get_Single_Anolog) refers to hardware_iic.o(.text.IIC_ReadBytes) for IIC_ReadBytes
    hardware_iic.o(.ARM.exidx.text.IIC_Get_Single_Anolog) refers to hardware_iic.o(.text.IIC_Get_Single_Anolog) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_Get_Normalize) refers to hardware_iic.o(.text.IIC_WriteBytes) for IIC_WriteBytes
    hardware_iic.o(.text.IIC_Get_Normalize) refers to time.o(.text.delay_ms) for delay_ms
    hardware_iic.o(.text.IIC_Get_Normalize) refers to hardware_iic.o(.text.IIC_ReadBytes) for IIC_ReadBytes
    hardware_iic.o(.text.IIC_Get_Normalize) refers to hardware_iic.o(.bss.IIC_write_buff) for IIC_write_buff
    hardware_iic.o(.ARM.exidx.text.IIC_Get_Normalize) refers to hardware_iic.o(.text.IIC_Get_Normalize) for [Anonymous Symbol]
    hardware_iic.o(.text.grayscale_detection_sensor) refers to printfa.o(i.__0sprintf) for sprintf
    hardware_iic.o(.text.grayscale_detection_sensor) refers to usart.o(.text.uart0_send_string) for uart0_send_string
    hardware_iic.o(.text.grayscale_detection_sensor) refers to memseta.o(.text) for __aeabi_memclr
    hardware_iic.o(.text.grayscale_detection_sensor) refers to hardware_iic.o(.text.Ping) for Ping
    hardware_iic.o(.text.grayscale_detection_sensor) refers to time.o(.text.delay_ms) for delay_ms
    hardware_iic.o(.text.grayscale_detection_sensor) refers to empty.o(.bss.temporary_buffer) for temporary_buffer
    hardware_iic.o(.text.grayscale_detection_sensor) refers to hardware_iic.o(.rodata.str1.1) for [Anonymous Symbol]
    hardware_iic.o(.ARM.exidx.text.grayscale_detection_sensor) refers to hardware_iic.o(.text.grayscale_detection_sensor) for [Anonymous Symbol]
    hw_i2c.o(.text.hardware_IIC_WirteByte) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for DL_I2C_fillControllerTXFIFO
    hw_i2c.o(.text.hardware_IIC_WirteByte) refers to hw_i2c.o(.text.DL_I2C_getControllerStatus) for DL_I2C_getControllerStatus
    hw_i2c.o(.text.hardware_IIC_WirteByte) refers to hw_i2c.o(.text.DL_I2C_startControllerTransfer) for DL_I2C_startControllerTransfer
    hw_i2c.o(.text.hardware_IIC_WirteByte) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for DL_I2C_flushControllerTXFIFO
    hw_i2c.o(.ARM.exidx.text.hardware_IIC_WirteByte) refers to hw_i2c.o(.text.hardware_IIC_WirteByte) for [Anonymous Symbol]
    hw_i2c.o(.ARM.exidx.text.DL_I2C_getControllerStatus) refers to hw_i2c.o(.text.DL_I2C_getControllerStatus) for [Anonymous Symbol]
    hw_i2c.o(.text.DL_I2C_startControllerTransfer) refers to hw_i2c.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    hw_i2c.o(.ARM.exidx.text.DL_I2C_startControllerTransfer) refers to hw_i2c.o(.text.DL_I2C_startControllerTransfer) for [Anonymous Symbol]
    hw_i2c.o(.text.hardware_IIC_WirteBytes) refers to memcpya.o(.text) for __aeabi_memcpy
    hw_i2c.o(.text.hardware_IIC_WirteBytes) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for DL_I2C_fillControllerTXFIFO
    hw_i2c.o(.text.hardware_IIC_WirteBytes) refers to hw_i2c.o(.text.DL_I2C_getControllerStatus) for DL_I2C_getControllerStatus
    hw_i2c.o(.text.hardware_IIC_WirteBytes) refers to hw_i2c.o(.text.DL_I2C_startControllerTransfer) for DL_I2C_startControllerTransfer
    hw_i2c.o(.text.hardware_IIC_WirteBytes) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for DL_I2C_flushControllerTXFIFO
    hw_i2c.o(.ARM.exidx.text.hardware_IIC_WirteBytes) refers to hw_i2c.o(.text.hardware_IIC_WirteBytes) for [Anonymous Symbol]
    hw_i2c.o(.text.hardware_IIC_ReadByte) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for DL_I2C_fillControllerTXFIFO
    hw_i2c.o(.text.hardware_IIC_ReadByte) refers to hw_i2c.o(.text.DL_I2C_getControllerStatus) for DL_I2C_getControllerStatus
    hw_i2c.o(.text.hardware_IIC_ReadByte) refers to hw_i2c.o(.text.DL_I2C_startControllerTransfer) for DL_I2C_startControllerTransfer
    hw_i2c.o(.text.hardware_IIC_ReadByte) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for DL_I2C_flushControllerTXFIFO
    hw_i2c.o(.text.hardware_IIC_ReadByte) refers to hw_i2c.o(.text.DL_I2C_receiveControllerData) for DL_I2C_receiveControllerData
    hw_i2c.o(.ARM.exidx.text.hardware_IIC_ReadByte) refers to hw_i2c.o(.text.hardware_IIC_ReadByte) for [Anonymous Symbol]
    hw_i2c.o(.ARM.exidx.text.DL_I2C_receiveControllerData) refers to hw_i2c.o(.text.DL_I2C_receiveControllerData) for [Anonymous Symbol]
    hw_i2c.o(.text.hardware_IIC_ReadBytes) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for DL_I2C_fillControllerTXFIFO
    hw_i2c.o(.text.hardware_IIC_ReadBytes) refers to hw_i2c.o(.text.DL_I2C_getControllerStatus) for DL_I2C_getControllerStatus
    hw_i2c.o(.text.hardware_IIC_ReadBytes) refers to hw_i2c.o(.text.DL_I2C_startControllerTransfer) for DL_I2C_startControllerTransfer
    hw_i2c.o(.text.hardware_IIC_ReadBytes) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for DL_I2C_flushControllerTXFIFO
    hw_i2c.o(.text.hardware_IIC_ReadBytes) refers to hw_i2c.o(.text.DL_I2C_receiveControllerData) for DL_I2C_receiveControllerData
    hw_i2c.o(.ARM.exidx.text.hardware_IIC_ReadBytes) refers to hw_i2c.o(.text.hardware_IIC_ReadBytes) for [Anonymous Symbol]
    hw_i2c.o(.ARM.exidx.text.DL_Common_updateReg) refers to hw_i2c.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    time.o(.ARM.exidx.text.delay_us) refers to time.o(.text.delay_us) for [Anonymous Symbol]
    time.o(.text.delay_ms) refers to time.o(.text.delay_us) for delay_us
    time.o(.ARM.exidx.text.delay_ms) refers to time.o(.text.delay_ms) for [Anonymous Symbol]
    sensor.o(.text.CalculatePositionFromDigital) refers to fflti.o(.text) for __aeabi_i2f
    sensor.o(.text.CalculatePositionFromDigital) refers to fadd.o(.text) for __aeabi_fadd
    sensor.o(.text.CalculatePositionFromDigital) refers to fdiv.o(.text) for __aeabi_fdiv
    sensor.o(.text.CalculatePositionFromDigital) refers to sensor.o(.rodata.position_weights) for [Anonymous Symbol]
    sensor.o(.ARM.exidx.text.CalculatePositionFromDigital) refers to sensor.o(.text.CalculatePositionFromDigital) for [Anonymous Symbol]
    sensor.o(.text.UpdatePosition) refers to hardware_iic.o(.text.IIC_Get_Digtal) for IIC_Get_Digtal
    sensor.o(.text.UpdatePosition) refers to sensor.o(.text.CalculatePositionFromDigital) for CalculatePositionFromDigital
    sensor.o(.ARM.exidx.text.UpdatePosition) refers to sensor.o(.text.UpdatePosition) for [Anonymous Symbol]
    dl_common.o(.ARM.exidx.text.DL_Common_delayCycles) refers to dl_common.o(.text.DL_Common_delayCycles) for [Anonymous Symbol]
    dl_dma.o(.ARM.exidx.text.DL_DMA_initChannel) refers to dl_dma.o(.text.DL_DMA_initChannel) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_setClockConfig) refers to dl_i2c.o(.text.DL_I2C_setClockConfig) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_getClockConfig) refers to dl_i2c.o(.text.DL_I2C_getClockConfig) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_fillControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerRXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_fillTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetRXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_init) refers to dl_spi.o(.text.DL_SPI_init) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_setClockConfig) refers to dl_spi.o(.text.DL_SPI_setClockConfig) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_getClockConfig) refers to dl_spi.o(.text.DL_SPI_getClockConfig) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking8) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking16) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking32) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking8) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking16) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking32) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck8) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck16) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck32) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck8) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck16) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck32) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO8) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO16) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO32) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO8) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO16) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_saveConfiguration) refers to dl_spi.o(.text.DL_SPI_saveConfiguration) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_restoreConfiguration) refers to dl_spi.o(.text.DL_SPI_restoreConfiguration) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO32) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO32) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig) refers to dl_timer.o(.text.DL_Timer_getClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Timer_initCaptureMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCaptureTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCompareMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode) refers to dl_timer.o(.text.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCompareTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcDn) refers to dl_timer.o(.text.DL_Timer_setSecondCompSrcDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcDn) refers to dl_timer.o(.text.DL_Timer_getSecondCompSrcDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcUp) refers to dl_timer.o(.text.DL_Timer_setSecondCompSrcUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcUp) refers to dl_timer.o(.text.DL_Timer_getSecondCompSrcUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionDn) refers to dl_timer.o(.text.DL_Timer_setSecondCompActionDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionDn) refers to dl_timer.o(.text.DL_Timer_getSecondCompActionDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionUp) refers to dl_timer.o(.text.DL_Timer_setSecondCompActionUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionUp) refers to dl_timer.o(.text.DL_Timer_getSecondCompActionUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompActUpdateMethod) refers to dl_timer.o(.text.DL_Timer_setCaptCompActUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompActUpdateMethod) refers to dl_timer.o(.text.DL_Timer_getCaptCompActUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut) refers to dl_timer.o(.text.DL_Timer_overrideCCPOut) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled) refers to dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration) refers to dl_timer.o(.text.DL_Timer_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration) refers to dl_timer.o(.text.DL_Timer_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initFourCCPWMMode) refers to dl_timer.o(.text.DL_Timer_initFourCCPWMMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_setFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_getFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration) refers to dl_timer.o(.text.DL_TimerA_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration) refers to dl_timer.o(.text.DL_TimerA_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode) refers to dl_timer.o(.text.DL_Timer_configQEIHallInputMode) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_init) refers to dl_uart.o(.text.DL_UART_init) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig) refers to dl_uart.o(.text.DL_UART_setClockConfig) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig) refers to dl_uart.o(.text.DL_UART_getClockConfig) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_configBaudRate) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate) refers to dl_uart.o(.text.DL_UART_configBaudRate) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode) refers to dl_uart.o(.text.DL_UART_configIrDAMode) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength) refers to dl_uart.o(.text.DL_UART_setIrDAPulseLength) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking) refers to dl_uart.o(.text.DL_UART_receiveDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck) refers to dl_uart.o(.text.DL_UART_receiveDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck) refers to dl_uart.o(.text.DL_UART_transmitDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO) refers to dl_uart.o(.text.DL_UART_drainRXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO) refers to dl_uart.o(.text.DL_UART_fillTXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Main_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Main_restoreConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_restoreConfiguration) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configSYSPLL) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setLFCLKSourceLFXT) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setLFCLKSourceLFXT) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXT) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXT) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXTParams) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configFCC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configFCC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicyRUNSLEEP) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicyRUNSLEEP) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTOP) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTOP) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTANDBY) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTANDBY) for [Anonymous Symbol]
    sqrt.o(i.__softfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.sqrt) refers to errno.o(i.__set_errno) for __set_errno
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to usart.o(.text.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to usart.o(.text.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to usart.o(.text.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to usart.o(.text.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    fmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers to fepilogue.o(.text) for _float_round
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    fcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ffixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to empty.o(.text.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to empty.o(.text.main) for main
    idiv_div0.o(.text) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    fepilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    depilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    depilogue.o(.text) refers to depilogue.o(i.__ARM_clz) for __ARM_clz
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dsqrt.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dsqrt.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dsqrt.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    depilogue.o(i.__ARM_clz) refers (Special) to iusefp.o(.text) for __I$use$fp


==============================================================================

Removing Unused input sections from the image.

    Removing empty.o(.text), (0 bytes).
    Removing empty.o(.ARM.exidx.text.main), (8 bytes).
    Removing empty.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ), (8 bytes).
    Removing empty.o(.ARM.exidx.text.__NVIC_EnableIRQ), (8 bytes).
    Removing empty.o(.ARM.exidx.text.PID_operation), (8 bytes).
    Removing empty.o(.ARM.exidx.text.TIMG0_IRQHandler), (8 bytes).
    Removing empty.o(.ARM.exidx.text.DL_Timer_getPendingInterrupt), (8 bytes).
    Removing empty.o(.ARM.use_no_argv), (4 bytes).
    Removing startup_mspm0g350x_uvision.o(HEAP), (1024 bytes).
    Removing ti_msp_dl_config.o(.text), (0 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_LED_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_TICK_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_I2C_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SPI_LCD_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DMA_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init), (8 bytes).
    Removing ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration), (128 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_saveConfiguration), (8 bytes).
    Removing ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration), (136 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_restoreConfiguration), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SPI_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SPI_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralAnalogFunction), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralOutputFunction), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableOutput), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralInputFunctionFeatures), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableHiZ), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralInputFunction), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalOutputFeatures), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalInputFeatures), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setLowerPinsPolarity), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setUpperPinsPolarity), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearInterruptStatus), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableInterrupt), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setBORThreshold), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setFlashWaitState), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setSYSOSCFreq), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableHFXT), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableSYSPLL), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setULPCLKDivider), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_enableMFCLK), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.__NVIC_SetPriority), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_setCounterControl), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enableClock), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_setCCPDirection), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enableInterrupt), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_setAnalogGlitchFilterPulseWidth), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_enableAnalogGlitchFilter), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_resetControllerTransfer), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_setTimerPeriod), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_setControllerTXFIFOThreshold), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_setControllerRXFIFOThreshold), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_enableControllerClockStretching), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_enableController), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setOversampling), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setBaudRateDivisor), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableInterrupt), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableFIFOs), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setRXFIFOThreshold), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setTXFIFOThreshold), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enable), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SPI_setBitRateSerialClockDivider), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SPI_setFIFOThreshold), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SPI_enable), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DMA_CH0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_DMA_clearInterruptStatus), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_DMA_enableInterrupt), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_DMA_setBurstSize), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSTICK_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSTICK_enable), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing led.o(.text), (0 bytes).
    Removing led.o(.text.LED_flash), (52 bytes).
    Removing led.o(.ARM.exidx.text.LED_flash), (8 bytes).
    Removing led.o(.text.DL_GPIO_clearPins), (20 bytes).
    Removing led.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing led.o(.text.DL_GPIO_setPins), (24 bytes).
    Removing led.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing key.o(.text), (0 bytes).
    Removing key.o(.text.KEY_control_LED), (48 bytes).
    Removing key.o(.ARM.exidx.text.KEY_control_LED), (8 bytes).
    Removing key.o(.text.DL_GPIO_readPins), (22 bytes).
    Removing key.o(.ARM.exidx.text.DL_GPIO_readPins), (8 bytes).
    Removing key.o(.text.DL_GPIO_setPins), (24 bytes).
    Removing key.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing key.o(.text.DL_GPIO_clearPins), (20 bytes).
    Removing key.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing key.o(.text.Get_KEY), (80 bytes).
    Removing key.o(.ARM.exidx.text.Get_KEY), (8 bytes).
    Removing usart.o(.text), (0 bytes).
    Removing usart.o(.ARM.exidx.text.uart0_send_char), (8 bytes).
    Removing usart.o(.ARM.exidx.text.DL_UART_isBusy), (8 bytes).
    Removing usart.o(.ARM.exidx.text.DL_UART_transmitData), (8 bytes).
    Removing usart.o(.ARM.exidx.text.uart0_send_string), (8 bytes).
    Removing usart.o(.text.fputc), (44 bytes).
    Removing usart.o(.ARM.exidx.text.fputc), (8 bytes).
    Removing usart.o(.ARM.exidx.text.uart_send), (8 bytes).
    Removing usart.o(.ARM.exidx.text.UART0_IRQHandler), (8 bytes).
    Removing usart.o(.ARM.exidx.text.DL_UART_getPendingInterrupt), (8 bytes).
    Removing pwm.o(.text), (0 bytes).
    Removing pwm.o(.text.PWM_LED), (120 bytes).
    Removing pwm.o(.ARM.exidx.text.PWM_LED), (8 bytes).
    Removing iic.o(.text), (0 bytes).
    Removing iic.o(.text.IIC_Start), (104 bytes).
    Removing iic.o(.ARM.exidx.text.IIC_Start), (8 bytes).
    Removing iic.o(.text.DL_GPIO_initDigitalOutput), (24 bytes).
    Removing iic.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput), (8 bytes).
    Removing iic.o(.text.DL_GPIO_setPins), (24 bytes).
    Removing iic.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing iic.o(.text.DL_GPIO_enableOutput), (24 bytes).
    Removing iic.o(.ARM.exidx.text.DL_GPIO_enableOutput), (8 bytes).
    Removing iic.o(.text.DL_GPIO_clearPins), (20 bytes).
    Removing iic.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing iic.o(.text.IIC_Stop), (88 bytes).
    Removing iic.o(.ARM.exidx.text.IIC_Stop), (8 bytes).
    Removing iic.o(.text.IIC_Send_Ack), (136 bytes).
    Removing iic.o(.ARM.exidx.text.IIC_Send_Ack), (8 bytes).
    Removing iic.o(.text.IIC_Wait_Ack), (196 bytes).
    Removing iic.o(.ARM.exidx.text.IIC_Wait_Ack), (8 bytes).
    Removing iic.o(.text.DL_GPIO_initDigitalInput), (28 bytes).
    Removing iic.o(.ARM.exidx.text.DL_GPIO_initDigitalInput), (8 bytes).
    Removing iic.o(.text.DL_GPIO_readPins), (22 bytes).
    Removing iic.o(.ARM.exidx.text.DL_GPIO_readPins), (8 bytes).
    Removing iic.o(.text.IIC_Send_Byte), (160 bytes).
    Removing iic.o(.ARM.exidx.text.IIC_Send_Byte), (8 bytes).
    Removing iic.o(.text.IIC_Read_Byte), (156 bytes).
    Removing iic.o(.ARM.exidx.text.IIC_Read_Byte), (8 bytes).
    Removing iic.o(.text.SHT20_Read), (248 bytes).
    Removing iic.o(.ARM.exidx.text.SHT20_Read), (8 bytes).
    Removing iic.o(.rodata.str1.1), (22 bytes).
    Removing hw_lcd.o(.text), (0 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.spi_write_bus), (8 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.DL_SPI_transmitData8), (8 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.DL_SPI_isBusy), (8 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_Writ_Bus), (8 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_WR_DATA8), (8 bytes).
    Removing hw_lcd.o(.text.LCD_WR_DATA), (28 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_WR_DATA), (8 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_WR_REG), (8 bytes).
    Removing hw_lcd.o(.text.LCD_Address_Set), (62 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_Address_Set), (8 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.lcd_init), (8 bytes).
    Removing hw_lcd.o(.text.LCD_Fill), (92 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_Fill), (8 bytes).
    Removing hw_lcd.o(.text.LCD_DrawPoint), (32 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_DrawPoint), (8 bytes).
    Removing hw_lcd.o(.text.LCD_DrawLine), (268 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_DrawLine), (8 bytes).
    Removing hw_lcd.o(.text.LCD_DrawVerrticalLine), (66 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_DrawVerrticalLine), (8 bytes).
    Removing hw_lcd.o(.text.LCD_DrawRectangle), (82 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_DrawRectangle), (8 bytes).
    Removing hw_lcd.o(.text.Draw_Circle), (220 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.Draw_Circle), (8 bytes).
    Removing hw_lcd.o(.text.Drawarc), (338 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.Drawarc), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ArcRect), (330 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ArcRect), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ShowChinese), (296 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ShowChinese), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ShowChinese12x12), (412 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ShowChinese12x12), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ShowChinese16x16), (412 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ShowChinese16x16), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ShowChinese24x24), (412 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ShowChinese24x24), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ShowChinese32x32), (412 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ShowChinese32x32), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ShowChar), (476 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ShowChar), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ShowString), (98 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ShowString), (8 bytes).
    Removing hw_lcd.o(.text.mypow), (48 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.mypow), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ShowIntNum), (244 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ShowIntNum), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ShowFloatNum1), (252 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ShowFloatNum1), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ShowPicture), (128 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ShowPicture), (8 bytes).
    Removing hw_lcd.o(.rodata.ascii_1206), (1140 bytes).
    Removing hw_lcd.o(.rodata.ascii_1608), (1520 bytes).
    Removing hw_lcd.o(.rodata.ascii_2412), (4560 bytes).
    Removing hw_lcd.o(.rodata.ascii_3216), (6080 bytes).
    Removing hw_lcd.o(.rodata.tfont12), (130 bytes).
    Removing hw_lcd.o(.rodata.tfont16), (374 bytes).
    Removing hw_lcd.o(.rodata.tfont24), (222 bytes).
    Removing hw_lcd.o(.rodata.tfont32), (130 bytes).
    Removing oled.o(.text), (0 bytes).
    Removing oled.o(.text.disp_x_center), (100 bytes).
    Removing oled.o(.ARM.exidx.text.disp_x_center), (8 bytes).
    Removing oled.o(.text.disp_string_rect), (136 bytes).
    Removing oled.o(.ARM.exidx.text.disp_string_rect), (8 bytes).
    Removing oled.o(.text.disp_select_box), (234 bytes).
    Removing oled.o(.ARM.exidx.text.disp_select_box), (8 bytes).
    Removing oled.o(.text.ui_home_page), (216 bytes).
    Removing oled.o(.ARM.exidx.text.ui_home_page), (8 bytes).
    Removing oled.o(.text.DL_GPIO_clearPins), (20 bytes).
    Removing oled.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing oled.o(.text.DL_GPIO_setPins), (24 bytes).
    Removing oled.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing oled.o(.rodata.str1.1), (37 bytes).
    Removing hw_key.o(.text), (0 bytes).
    Removing hw_key.o(.text.key_scan), (140 bytes).
    Removing hw_key.o(.ARM.exidx.text.key_scan), (8 bytes).
    Removing hw_key.o(.text.DL_GPIO_readPins), (22 bytes).
    Removing hw_key.o(.ARM.exidx.text.DL_GPIO_readPins), (8 bytes).
    Removing pid.o(.text), (0 bytes).
    Removing pid.o(.ARM.exidx.text.PID_Init), (8 bytes).
    Removing pid.o(.ARM.exidx.text.PID_Calculate), (8 bytes).
    Removing bsp_motor_hallencoder.o(.text), (0 bytes).
    Removing bsp_motor_hallencoder.o(.ARM.exidx.text.encoder_init), (8 bytes).
    Removing bsp_motor_hallencoder.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ), (8 bytes).
    Removing bsp_motor_hallencoder.o(.ARM.exidx.text.__NVIC_EnableIRQ), (8 bytes).
    Removing bsp_motor_hallencoder.o(.ARM.exidx.text.get_encoder_count_L), (8 bytes).
    Removing bsp_motor_hallencoder.o(.ARM.exidx.text.get_encoder_count_R), (8 bytes).
    Removing bsp_motor_hallencoder.o(.text.get_encoder_dir_L), (12 bytes).
    Removing bsp_motor_hallencoder.o(.ARM.exidx.text.get_encoder_dir_L), (8 bytes).
    Removing bsp_motor_hallencoder.o(.text.get_encoder_dir_R), (12 bytes).
    Removing bsp_motor_hallencoder.o(.ARM.exidx.text.get_encoder_dir_R), (8 bytes).
    Removing bsp_motor_hallencoder.o(.ARM.exidx.text.encoder_update_L), (8 bytes).
    Removing bsp_motor_hallencoder.o(.ARM.exidx.text.encoder_update_R), (8 bytes).
    Removing bsp_motor_hallencoder.o(.ARM.exidx.text.GROUP1_IRQHandler), (8 bytes).
    Removing bsp_motor_hallencoder.o(.ARM.exidx.text.DL_GPIO_getEnabledInterruptStatus), (8 bytes).
    Removing bsp_motor_hallencoder.o(.ARM.exidx.text.DL_GPIO_readPins), (8 bytes).
    Removing bsp_motor_hallencoder.o(.ARM.exidx.text.DL_GPIO_clearInterruptStatus), (8 bytes).
    Removing bsp_tb6612.o(.text), (0 bytes).
    Removing bsp_tb6612.o(.text.TB6612_Motor_Stop), (56 bytes).
    Removing bsp_tb6612.o(.ARM.exidx.text.TB6612_Motor_Stop), (8 bytes).
    Removing bsp_tb6612.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing bsp_tb6612.o(.ARM.exidx.text.AB_Control), (8 bytes).
    Removing bsp_tb6612.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing hw_timer.o(.text), (0 bytes).
    Removing hw_timer.o(.ARM.exidx.text.timer_init), (8 bytes).
    Removing hw_timer.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ), (8 bytes).
    Removing hw_timer.o(.ARM.exidx.text.__NVIC_EnableIRQ), (8 bytes).
    Removing hw_timer.o(.ARM.exidx.text.TIMA0_IRQHandler), (8 bytes).
    Removing hw_timer.o(.ARM.exidx.text.DL_Timer_getPendingInterrupt), (8 bytes).
    Removing filter.o(.text), (0 bytes).
    Removing filter.o(.ARM.exidx.text.Kalman_Init), (8 bytes).
    Removing filter.o(.ARM.exidx.text.Kalman_Update), (8 bytes).
    Removing hardware_iic.o(.text), (0 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_ReadByte), (8 bytes).
    Removing hardware_iic.o(.text.IIC_ReadBytes), (44 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_ReadBytes), (8 bytes).
    Removing hardware_iic.o(.text.IIC_WriteByte), (36 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_WriteByte), (8 bytes).
    Removing hardware_iic.o(.text.IIC_WriteBytes), (44 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_WriteBytes), (8 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.Ping), (8 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_Get_Digtal), (8 bytes).
    Removing hardware_iic.o(.text.IIC_Get_Anolog), (52 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_Get_Anolog), (8 bytes).
    Removing hardware_iic.o(.text.IIC_Get_Single_Anolog), (34 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_Get_Single_Anolog), (8 bytes).
    Removing hardware_iic.o(.text.IIC_Get_Normalize), (84 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_Get_Normalize), (8 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.grayscale_detection_sensor), (8 bytes).
    Removing hardware_iic.o(.bss.IIC_write_buff), (10 bytes).
    Removing hw_i2c.o(.text), (0 bytes).
    Removing hw_i2c.o(.text.hardware_IIC_WirteByte), (120 bytes).
    Removing hw_i2c.o(.ARM.exidx.text.hardware_IIC_WirteByte), (8 bytes).
    Removing hw_i2c.o(.ARM.exidx.text.DL_I2C_getControllerStatus), (8 bytes).
    Removing hw_i2c.o(.ARM.exidx.text.DL_I2C_startControllerTransfer), (8 bytes).
    Removing hw_i2c.o(.text.hardware_IIC_WirteBytes), (196 bytes).
    Removing hw_i2c.o(.ARM.exidx.text.hardware_IIC_WirteBytes), (8 bytes).
    Removing hw_i2c.o(.ARM.exidx.text.hardware_IIC_ReadByte), (8 bytes).
    Removing hw_i2c.o(.ARM.exidx.text.DL_I2C_receiveControllerData), (8 bytes).
    Removing hw_i2c.o(.text.hardware_IIC_ReadBytes), (220 bytes).
    Removing hw_i2c.o(.ARM.exidx.text.hardware_IIC_ReadBytes), (8 bytes).
    Removing hw_i2c.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing time.o(.text), (0 bytes).
    Removing time.o(.ARM.exidx.text.delay_us), (8 bytes).
    Removing time.o(.ARM.exidx.text.delay_ms), (8 bytes).
    Removing sensor.o(.text), (0 bytes).
    Removing sensor.o(.ARM.exidx.text.CalculatePositionFromDigital), (8 bytes).
    Removing sensor.o(.ARM.exidx.text.UpdatePosition), (8 bytes).
    Removing dl_common.o(.text), (0 bytes).
    Removing dl_common.o(.ARM.exidx.text.DL_Common_delayCycles), (8 bytes).
    Removing dl_dma.o(.text), (0 bytes).
    Removing dl_dma.o(.ARM.exidx.text.DL_DMA_initChannel), (8 bytes).
    Removing dl_i2c.o(.text), (0 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_setClockConfig), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_getClockConfig), (26 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_getClockConfig), (8 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_fillControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO), (48 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_fillTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO), (40 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking), (20 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataBlocking), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataCheck), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking), (20 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataBlocking), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataCheck), (8 bytes).
    Removing dl_spi.o(.text), (0 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_init), (8 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_setClockConfig), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_getClockConfig), (16 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_getClockConfig), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking8), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking16), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking32), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking8), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking16), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking32), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck8), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck16), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck32), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck8), (24 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck16), (24 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck32), (24 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO8), (40 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO16), (44 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO32), (40 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO8), (40 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO16), (44 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_saveConfiguration), (80 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_saveConfiguration), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_restoreConfiguration), (112 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_restoreConfiguration), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO32), (40 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO32), (8 bytes).
    Removing dl_timer.o(.text), (0 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getClockConfig), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareCtl), (40 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureMode), (300 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInput), (26 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureTriggerMode), (124 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureCombinedMode), (232 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareMode), (168 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareTriggerMode), (112 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareValue), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareCtl), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompSrcDn), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompSrcDn), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompSrcUp), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompSrcUp), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompActionDn), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompActionDn), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompActionUp), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompActionUp), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptCompActUpdateMethod), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompActUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptCompActUpdateMethod), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompActUpdateMethod), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareAction), (36 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareAction), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_overrideCCPOut), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInput), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter), (18 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_saveConfiguration), (236 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_restoreConfiguration), (244 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initFourCCPWMMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setFaultSourceConfig), (44 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getFaultSourceConfig), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_saveConfiguration), (264 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_restoreConfiguration), (276 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_configQEIHallInputMode), (36 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode), (8 bytes).
    Removing dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode), (12 bytes).
    Removing dl_uart.o(.text), (0 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_init), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_getClockConfig), (16 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configBaudRate), (140 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configIrDAMode), (60 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_setIrDAPulseLength), (42 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataBlocking), (20 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataBlocking), (20 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataCheck), (28 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_drainRXFIFO), (40 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_fillTXFIFO), (40 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_saveConfiguration), (88 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_restoreConfiguration), (120 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_saveConfiguration), (104 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_restoreConfiguration), (132 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text), (0 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configSYSPLL), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setLFCLKSourceLFXT), (80 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setLFCLKSourceLFXT), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK), (60 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC), (40 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC), (32 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXT), (76 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXT), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXTParams), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configFCC), (28 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configFCC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicyRUNSLEEP), (48 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicyRUNSLEEP), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTOP), (52 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTOP), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTANDBY), (40 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTANDBY), (8 bytes).
    Removing dfltui.o(.text), (28 bytes).
    Removing ffixui.o(.text), (40 bytes).
    Removing d2f.o(.text), (56 bytes).
    Removing dsqrt.o(.text), (162 bytes).

512 unused section(s) (total 31549 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast_div0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv_div0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  idiv_div0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  idiv0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strlen.c         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  fadd.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  fcmple.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixui.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  fflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpsqrt.c               0x00000000   Number         0  dsqrt.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    Filter.c                                 0x00000000   Number         0  filter.o ABSOLUTE
    KEY.c                                    0x00000000   Number         0  key.o ABSOLUTE
    LED.c                                    0x00000000   Number         0  led.o ABSOLUTE
    OLED.c                                   0x00000000   Number         0  oled.o ABSOLUTE
    PID.c                                    0x00000000   Number         0  pid.o ABSOLUTE
    PWM.c                                    0x00000000   Number         0  pwm.o ABSOLUTE
    Sensor.c                                 0x00000000   Number         0  sensor.o ABSOLUTE
    Time.c                                   0x00000000   Number         0  time.o ABSOLUTE
    USART.c                                  0x00000000   Number         0  usart.o ABSOLUTE
    bsp_motor_hallencoder.c                  0x00000000   Number         0  bsp_motor_hallencoder.o ABSOLUTE
    bsp_tb6612.c                             0x00000000   Number         0  bsp_tb6612.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    dl_common.c                              0x00000000   Number         0  dl_common.o ABSOLUTE
    dl_dma.c                                 0x00000000   Number         0  dl_dma.o ABSOLUTE
    dl_i2c.c                                 0x00000000   Number         0  dl_i2c.o ABSOLUTE
    dl_spi.c                                 0x00000000   Number         0  dl_spi.o ABSOLUTE
    dl_sysctl_mspm0g1x0x_g3x0x.c             0x00000000   Number         0  dl_sysctl_mspm0g1x0x_g3x0x.o ABSOLUTE
    dl_timer.c                               0x00000000   Number         0  dl_timer.o ABSOLUTE
    dl_uart.c                                0x00000000   Number         0  dl_uart.o ABSOLUTE
    empty.c                                  0x00000000   Number         0  empty.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    hardware_iic.c                           0x00000000   Number         0  hardware_iic.o ABSOLUTE
    hw_i2c.c                                 0x00000000   Number         0  hw_i2c.o ABSOLUTE
    hw_key.c                                 0x00000000   Number         0  hw_key.o ABSOLUTE
    hw_lcd.c                                 0x00000000   Number         0  hw_lcd.o ABSOLUTE
    hw_timer.c                               0x00000000   Number         0  hw_timer.o ABSOLUTE
    iic.c                                    0x00000000   Number         0  iic.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    startup_mspm0g350x_uvision.s             0x00000000   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    ti_msp_dl_config.c                       0x00000000   Number         0  ti_msp_dl_config.o ABSOLUTE
    RESET                                    0x00000000   Section      192  startup_mspm0g350x_uvision.o(RESET)
    .ARM.Collect$$$$00000000                 0x000000c0   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x000000c0   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x000000c4   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x000000c8   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x000000c8   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x000000c8   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    __lit__00000000                          0x000000d0   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .ARM.Collect$$$$0000000D                 0x000000d0   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x000000d0   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x000000d0   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x000000d4   Section       20  startup_mspm0g350x_uvision.o(.text)
    .text                                    0x000000e8   Section        0  memseta.o(.text)
    .text                                    0x0000010c   Section        0  strlen.o(.text)
    .text                                    0x0000011a   Section        0  fadd.o(.text)
    .text                                    0x000001cc   Section        0  fmul.o(.text)
    .text                                    0x00000246   Section        0  fdiv.o(.text)
    .text                                    0x000002c4   Section        0  dadd.o(.text)
    .text                                    0x00000428   Section        0  dmul.o(.text)
    .text                                    0x000004f8   Section        0  ddiv.o(.text)
    .text                                    0x000005e8   Section        0  fcmple.o(.text)
    .text                                    0x00000604   Section        0  fflti.o(.text)
    .text                                    0x0000061c   Section        0  dflti.o(.text)
    .text                                    0x00000644   Section        0  ffixi.o(.text)
    .text                                    0x00000678   Section        0  dfixi.o(.text)
    .text                                    0x000006c0   Section        0  f2d.o(.text)
    .text                                    0x000006e8   Section        0  uidiv_div0.o(.text)
    .text                                    0x00000726   Section        0  uldiv.o(.text)
    .text                                    0x00000786   Section        0  llshl.o(.text)
    .text                                    0x000007a6   Section        0  llushr.o(.text)
    .text                                    0x000007c8   Section        0  llsshr.o(.text)
    .text                                    0x000007ee   Section        0  iusefp.o(.text)
    .text                                    0x000007ee   Section        0  fepilogue.o(.text)
    .text                                    0x00000870   Section        0  depilogue.o(.text)
    .text                                    0x00000930   Section        0  dfixul.o(.text)
    .text                                    0x00000970   Section       40  cdrcmple.o(.text)
    .text                                    0x00000998   Section       48  init.o(.text)
    [Anonymous Symbol]                       0x000009c8   Section        0  bsp_tb6612.o(.text.AB_Control)
    __arm_cp.2_0                             0x00000a70   Number         4  bsp_tb6612.o(.text.AB_Control)
    __arm_cp.2_1                             0x00000a74   Number         4  bsp_tb6612.o(.text.AB_Control)
    [Anonymous Symbol]                       0x00000a78   Section        0  sensor.o(.text.CalculatePositionFromDigital)
    __arm_cp.0_0                             0x00000af4   Number         4  sensor.o(.text.CalculatePositionFromDigital)
    [Anonymous Symbol]                       0x00000af8   Section        0  dl_common.o(.text.DL_Common_delayCycles)
    DL_Common_updateReg                      0x00000b03   Thumb Code    40  ti_msp_dl_config.o(.text.DL_Common_updateReg)
    [Anonymous Symbol]                       0x00000b02   Section        0  ti_msp_dl_config.o(.text.DL_Common_updateReg)
    DL_Common_updateReg                      0x00000b2b   Thumb Code    40  hw_i2c.o(.text.DL_Common_updateReg)
    [Anonymous Symbol]                       0x00000b2a   Section        0  hw_i2c.o(.text.DL_Common_updateReg)
    DL_DMA_clearInterruptStatus              0x00000b55   Thumb Code    20  ti_msp_dl_config.o(.text.DL_DMA_clearInterruptStatus)
    [Anonymous Symbol]                       0x00000b54   Section        0  ti_msp_dl_config.o(.text.DL_DMA_clearInterruptStatus)
    DL_DMA_enableInterrupt                   0x00000b69   Thumb Code    24  ti_msp_dl_config.o(.text.DL_DMA_enableInterrupt)
    [Anonymous Symbol]                       0x00000b68   Section        0  ti_msp_dl_config.o(.text.DL_DMA_enableInterrupt)
    [Anonymous Symbol]                       0x00000b80   Section        0  dl_dma.o(.text.DL_DMA_initChannel)
    __arm_cp.0_0                             0x00000bc0   Number         4  dl_dma.o(.text.DL_DMA_initChannel)
    DL_DMA_setBurstSize                      0x00000bc5   Thumb Code    30  ti_msp_dl_config.o(.text.DL_DMA_setBurstSize)
    [Anonymous Symbol]                       0x00000bc4   Section        0  ti_msp_dl_config.o(.text.DL_DMA_setBurstSize)
    DL_GPIO_clearInterruptStatus             0x00000be5   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus)
    [Anonymous Symbol]                       0x00000be4   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus)
    DL_GPIO_clearInterruptStatus             0x00000bfd   Thumb Code    24  bsp_motor_hallencoder.o(.text.DL_GPIO_clearInterruptStatus)
    [Anonymous Symbol]                       0x00000bfc   Section        0  bsp_motor_hallencoder.o(.text.DL_GPIO_clearInterruptStatus)
    __arm_cp.12_0                            0x00000c14   Number         4  bsp_motor_hallencoder.o(.text.DL_GPIO_clearInterruptStatus)
    DL_GPIO_clearPins                        0x00000c19   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_clearPins)
    [Anonymous Symbol]                       0x00000c18   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_clearPins)
    DL_GPIO_clearPins                        0x00000c2d   Thumb Code    20  hw_lcd.o(.text.DL_GPIO_clearPins)
    [Anonymous Symbol]                       0x00000c2c   Section        0  hw_lcd.o(.text.DL_GPIO_clearPins)
    DL_GPIO_clearPins                        0x00000c41   Thumb Code    20  bsp_tb6612.o(.text.DL_GPIO_clearPins)
    [Anonymous Symbol]                       0x00000c40   Section        0  bsp_tb6612.o(.text.DL_GPIO_clearPins)
    DL_GPIO_enableHiZ                        0x00000c55   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_enableHiZ)
    [Anonymous Symbol]                       0x00000c54   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_enableHiZ)
    DL_GPIO_enableInterrupt                  0x00000c6d   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt)
    [Anonymous Symbol]                       0x00000c6c   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt)
    __arm_cp.39_0                            0x00000c84   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt)
    DL_GPIO_enableOutput                     0x00000c89   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_enableOutput)
    [Anonymous Symbol]                       0x00000c88   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_enableOutput)
    __arm_cp.27_0                            0x00000c9c   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_enableOutput)
    DL_GPIO_enablePower                      0x00000ca1   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_enablePower)
    [Anonymous Symbol]                       0x00000ca0   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_enablePower)
    DL_GPIO_getEnabledInterruptStatus        0x00000cb5   Thumb Code    20  bsp_motor_hallencoder.o(.text.DL_GPIO_getEnabledInterruptStatus)
    [Anonymous Symbol]                       0x00000cb4   Section        0  bsp_motor_hallencoder.o(.text.DL_GPIO_getEnabledInterruptStatus)
    __arm_cp.10_0                            0x00000cc8   Number         4  bsp_motor_hallencoder.o(.text.DL_GPIO_getEnabledInterruptStatus)
    DL_GPIO_initDigitalInputFeatures         0x00000ccd   Thumb Code    44  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures)
    [Anonymous Symbol]                       0x00000ccc   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures)
    __arm_cp.32_0                            0x00000cf8   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures)
    DL_GPIO_initDigitalOutput                0x00000cfd   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput)
    [Anonymous Symbol]                       0x00000cfc   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput)
    DL_GPIO_initDigitalOutputFeatures        0x00000d11   Thumb Code    44  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutputFeatures)
    [Anonymous Symbol]                       0x00000d10   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutputFeatures)
    DL_GPIO_initPeripheralAnalogFunction     0x00000d3d   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralAnalogFunction)
    [Anonymous Symbol]                       0x00000d3c   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralAnalogFunction)
    DL_GPIO_initPeripheralInputFunction      0x00000d51   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction)
    [Anonymous Symbol]                       0x00000d50   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction)
    DL_GPIO_initPeripheralInputFunctionFeatures 0x00000d69   Thumb Code    52  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunctionFeatures)
    [Anonymous Symbol]                       0x00000d68   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunctionFeatures)
    __arm_cp.28_0                            0x00000d9c   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunctionFeatures)
    DL_GPIO_initPeripheralOutputFunction     0x00000da1   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction)
    [Anonymous Symbol]                       0x00000da0   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction)
    __arm_cp.26_0                            0x00000db8   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction)
    DL_GPIO_readPins                         0x00000dbd   Thumb Code    22  bsp_motor_hallencoder.o(.text.DL_GPIO_readPins)
    [Anonymous Symbol]                       0x00000dbc   Section        0  bsp_motor_hallencoder.o(.text.DL_GPIO_readPins)
    DL_GPIO_reset                            0x00000dd5   Thumb Code    16  ti_msp_dl_config.o(.text.DL_GPIO_reset)
    [Anonymous Symbol]                       0x00000dd4   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_reset)
    DL_GPIO_setLowerPinsPolarity             0x00000de5   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_setLowerPinsPolarity)
    [Anonymous Symbol]                       0x00000de4   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_setLowerPinsPolarity)
    __arm_cp.36_0                            0x00000dfc   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_setLowerPinsPolarity)
    DL_GPIO_setPins                          0x00000e01   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_setPins)
    [Anonymous Symbol]                       0x00000e00   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_setPins)
    DL_GPIO_setPins                          0x00000e15   Thumb Code    20  hw_lcd.o(.text.DL_GPIO_setPins)
    [Anonymous Symbol]                       0x00000e14   Section        0  hw_lcd.o(.text.DL_GPIO_setPins)
    DL_GPIO_setPins                          0x00000e29   Thumb Code    20  bsp_tb6612.o(.text.DL_GPIO_setPins)
    [Anonymous Symbol]                       0x00000e28   Section        0  bsp_tb6612.o(.text.DL_GPIO_setPins)
    __arm_cp.1_0                             0x00000e3c   Number         4  bsp_tb6612.o(.text.DL_GPIO_setPins)
    DL_GPIO_setUpperPinsPolarity             0x00000e41   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_setUpperPinsPolarity)
    [Anonymous Symbol]                       0x00000e40   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_setUpperPinsPolarity)
    DL_I2C_enableAnalogGlitchFilter          0x00000e59   Thumb Code    24  ti_msp_dl_config.o(.text.DL_I2C_enableAnalogGlitchFilter)
    [Anonymous Symbol]                       0x00000e58   Section        0  ti_msp_dl_config.o(.text.DL_I2C_enableAnalogGlitchFilter)
    DL_I2C_enableController                  0x00000e71   Thumb Code    20  ti_msp_dl_config.o(.text.DL_I2C_enableController)
    [Anonymous Symbol]                       0x00000e70   Section        0  ti_msp_dl_config.o(.text.DL_I2C_enableController)
    DL_I2C_enableControllerClockStretching   0x00000e85   Thumb Code    20  ti_msp_dl_config.o(.text.DL_I2C_enableControllerClockStretching)
    [Anonymous Symbol]                       0x00000e84   Section        0  ti_msp_dl_config.o(.text.DL_I2C_enableControllerClockStretching)
    __arm_cp.58_0                            0x00000e98   Number         4  ti_msp_dl_config.o(.text.DL_I2C_enableControllerClockStretching)
    DL_I2C_enablePower                       0x00000e9d   Thumb Code    20  ti_msp_dl_config.o(.text.DL_I2C_enablePower)
    [Anonymous Symbol]                       0x00000e9c   Section        0  ti_msp_dl_config.o(.text.DL_I2C_enablePower)
    __arm_cp.22_0                            0x00000eb0   Number         4  ti_msp_dl_config.o(.text.DL_I2C_enablePower)
    [Anonymous Symbol]                       0x00000eb4   Section        0  dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO)
    [Anonymous Symbol]                       0x00000ee0   Section        0  dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO)
    DL_I2C_getControllerStatus               0x00000f05   Thumb Code    16  hw_i2c.o(.text.DL_I2C_getControllerStatus)
    [Anonymous Symbol]                       0x00000f04   Section        0  hw_i2c.o(.text.DL_I2C_getControllerStatus)
    __arm_cp.1_0                             0x00000f14   Number         4  hw_i2c.o(.text.DL_I2C_getControllerStatus)
    DL_I2C_receiveControllerData             0x00000f19   Thumb Code    16  hw_i2c.o(.text.DL_I2C_receiveControllerData)
    [Anonymous Symbol]                       0x00000f18   Section        0  hw_i2c.o(.text.DL_I2C_receiveControllerData)
    __arm_cp.5_0                             0x00000f28   Number         4  hw_i2c.o(.text.DL_I2C_receiveControllerData)
    DL_I2C_reset                             0x00000f2d   Thumb Code    16  ti_msp_dl_config.o(.text.DL_I2C_reset)
    [Anonymous Symbol]                       0x00000f2c   Section        0  ti_msp_dl_config.o(.text.DL_I2C_reset)
    DL_I2C_resetControllerTransfer           0x00000f3d   Thumb Code    16  ti_msp_dl_config.o(.text.DL_I2C_resetControllerTransfer)
    [Anonymous Symbol]                       0x00000f3c   Section        0  ti_msp_dl_config.o(.text.DL_I2C_resetControllerTransfer)
    DL_I2C_setAnalogGlitchFilterPulseWidth   0x00000f4d   Thumb Code    38  ti_msp_dl_config.o(.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
    [Anonymous Symbol]                       0x00000f4c   Section        0  ti_msp_dl_config.o(.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
    [Anonymous Symbol]                       0x00000f72   Section        0  dl_i2c.o(.text.DL_I2C_setClockConfig)
    DL_I2C_setControllerRXFIFOThreshold      0x00000f99   Thumb Code    36  ti_msp_dl_config.o(.text.DL_I2C_setControllerRXFIFOThreshold)
    [Anonymous Symbol]                       0x00000f98   Section        0  ti_msp_dl_config.o(.text.DL_I2C_setControllerRXFIFOThreshold)
    DL_I2C_setControllerTXFIFOThreshold      0x00000fbd   Thumb Code    36  ti_msp_dl_config.o(.text.DL_I2C_setControllerTXFIFOThreshold)
    [Anonymous Symbol]                       0x00000fbc   Section        0  ti_msp_dl_config.o(.text.DL_I2C_setControllerTXFIFOThreshold)
    __arm_cp.56_0                            0x00000fe0   Number         4  ti_msp_dl_config.o(.text.DL_I2C_setControllerTXFIFOThreshold)
    DL_I2C_setTimerPeriod                    0x00000fe5   Thumb Code    20  ti_msp_dl_config.o(.text.DL_I2C_setTimerPeriod)
    [Anonymous Symbol]                       0x00000fe4   Section        0  ti_msp_dl_config.o(.text.DL_I2C_setTimerPeriod)
    __arm_cp.55_0                            0x00000ff8   Number         4  ti_msp_dl_config.o(.text.DL_I2C_setTimerPeriod)
    DL_I2C_startControllerTransfer           0x00000ffd   Thumb Code    64  hw_i2c.o(.text.DL_I2C_startControllerTransfer)
    [Anonymous Symbol]                       0x00000ffc   Section        0  hw_i2c.o(.text.DL_I2C_startControllerTransfer)
    __arm_cp.2_0                             0x0000103c   Number         4  hw_i2c.o(.text.DL_I2C_startControllerTransfer)
    __arm_cp.2_1                             0x00001040   Number         4  hw_i2c.o(.text.DL_I2C_startControllerTransfer)
    __arm_cp.2_2                             0x00001044   Number         4  hw_i2c.o(.text.DL_I2C_startControllerTransfer)
    __arm_cp.2_3                             0x00001048   Number         4  hw_i2c.o(.text.DL_I2C_startControllerTransfer)
    DL_SPI_enable                            0x0000104d   Thumb Code    20  ti_msp_dl_config.o(.text.DL_SPI_enable)
    [Anonymous Symbol]                       0x0000104c   Section        0  ti_msp_dl_config.o(.text.DL_SPI_enable)
    __arm_cp.69_0                            0x00001060   Number         4  ti_msp_dl_config.o(.text.DL_SPI_enable)
    DL_SPI_enablePower                       0x00001065   Thumb Code    20  ti_msp_dl_config.o(.text.DL_SPI_enablePower)
    [Anonymous Symbol]                       0x00001064   Section        0  ti_msp_dl_config.o(.text.DL_SPI_enablePower)
    __arm_cp.24_0                            0x00001078   Number         4  ti_msp_dl_config.o(.text.DL_SPI_enablePower)
    [Anonymous Symbol]                       0x0000107c   Section        0  dl_spi.o(.text.DL_SPI_init)
    __arm_cp.0_0                             0x000010b8   Number         4  dl_spi.o(.text.DL_SPI_init)
    __arm_cp.0_1                             0x000010bc   Number         4  dl_spi.o(.text.DL_SPI_init)
    DL_SPI_isBusy                            0x000010c1   Thumb Code    20  hw_lcd.o(.text.DL_SPI_isBusy)
    [Anonymous Symbol]                       0x000010c0   Section        0  hw_lcd.o(.text.DL_SPI_isBusy)
    __arm_cp.2_0                             0x000010d4   Number         4  hw_lcd.o(.text.DL_SPI_isBusy)
    DL_SPI_reset                             0x000010d9   Thumb Code    16  ti_msp_dl_config.o(.text.DL_SPI_reset)
    [Anonymous Symbol]                       0x000010d8   Section        0  ti_msp_dl_config.o(.text.DL_SPI_reset)
    __arm_cp.19_0                            0x000010e8   Number         4  ti_msp_dl_config.o(.text.DL_SPI_reset)
    __arm_cp.19_1                            0x000010ec   Number         4  ti_msp_dl_config.o(.text.DL_SPI_reset)
    DL_SPI_setBitRateSerialClockDivider      0x000010f1   Thumb Code    28  ti_msp_dl_config.o(.text.DL_SPI_setBitRateSerialClockDivider)
    [Anonymous Symbol]                       0x000010f0   Section        0  ti_msp_dl_config.o(.text.DL_SPI_setBitRateSerialClockDivider)
    __arm_cp.67_1                            0x0000110c   Number         4  ti_msp_dl_config.o(.text.DL_SPI_setBitRateSerialClockDivider)
    [Anonymous Symbol]                       0x00001110   Section        0  dl_spi.o(.text.DL_SPI_setClockConfig)
    DL_SPI_setFIFOThreshold                  0x00001125   Thumb Code    44  ti_msp_dl_config.o(.text.DL_SPI_setFIFOThreshold)
    [Anonymous Symbol]                       0x00001124   Section        0  ti_msp_dl_config.o(.text.DL_SPI_setFIFOThreshold)
    __arm_cp.68_0                            0x00001150   Number         4  ti_msp_dl_config.o(.text.DL_SPI_setFIFOThreshold)
    DL_SPI_transmitData8                     0x00001155   Thumb Code    22  hw_lcd.o(.text.DL_SPI_transmitData8)
    [Anonymous Symbol]                       0x00001154   Section        0  hw_lcd.o(.text.DL_SPI_transmitData8)
    [Anonymous Symbol]                       0x0000116c   Section        0  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_2                             0x00001220   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_3                             0x00001224   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_4                             0x00001228   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    DL_SYSCTL_disableHFXT                    0x0000122d   Thumb Code    12  ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT)
    [Anonymous Symbol]                       0x0000122c   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT)
    DL_SYSCTL_disableSYSPLL                  0x00001239   Thumb Code    16  ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL)
    [Anonymous Symbol]                       0x00001238   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL)
    __arm_cp.44_0                            0x00001248   Number         4  ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL)
    DL_SYSCTL_enableMFCLK                    0x0000124d   Thumb Code    16  ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFCLK)
    [Anonymous Symbol]                       0x0000124c   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFCLK)
    DL_SYSCTL_setBORThreshold                0x0000125d   Thumb Code    20  ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold)
    [Anonymous Symbol]                       0x0000125c   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold)
    __arm_cp.40_0                            0x00001270   Number         4  ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold)
    DL_SYSCTL_setFlashWaitState              0x00001275   Thumb Code    28  ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState)
    [Anonymous Symbol]                       0x00001274   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState)
    [Anonymous Symbol]                       0x00001290   Section        0  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
    __arm_cp.7_0                             0x000012dc   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
    DL_SYSCTL_setSYSOSCFreq                  0x000012e1   Thumb Code    24  ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq)
    [Anonymous Symbol]                       0x000012e0   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq)
    __arm_cp.42_0                            0x000012f8   Number         4  ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq)
    DL_SYSCTL_setULPCLKDivider               0x000012fd   Thumb Code    24  ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider)
    [Anonymous Symbol]                       0x000012fc   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider)
    [Anonymous Symbol]                       0x00001314   Section        0  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    __arm_cp.4_0                             0x00001334   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    __arm_cp.4_1                             0x00001338   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    DL_SYSTICK_enable                        0x0000133d   Thumb Code    12  ti_msp_dl_config.o(.text.DL_SYSTICK_enable)
    [Anonymous Symbol]                       0x0000133c   Section        0  ti_msp_dl_config.o(.text.DL_SYSTICK_enable)
    DL_SYSTICK_init                          0x00001349   Thumb Code    28  ti_msp_dl_config.o(.text.DL_SYSTICK_init)
    [Anonymous Symbol]                       0x00001348   Section        0  ti_msp_dl_config.o(.text.DL_SYSTICK_init)
    __arm_cp.74_0                            0x00001364   Number         4  ti_msp_dl_config.o(.text.DL_SYSTICK_init)
    __arm_cp.74_1                            0x00001368   Number         4  ti_msp_dl_config.o(.text.DL_SYSTICK_init)
    __arm_cp.74_2                            0x0000136c   Number         4  ti_msp_dl_config.o(.text.DL_SYSTICK_init)
    DL_Timer_enableClock                     0x00001371   Thumb Code    16  ti_msp_dl_config.o(.text.DL_Timer_enableClock)
    [Anonymous Symbol]                       0x00001370   Section        0  ti_msp_dl_config.o(.text.DL_Timer_enableClock)
    __arm_cp.49_0                            0x00001380   Number         4  ti_msp_dl_config.o(.text.DL_Timer_enableClock)
    DL_Timer_enableInterrupt                 0x00001385   Thumb Code    24  ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt)
    [Anonymous Symbol]                       0x00001384   Section        0  ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt)
    DL_Timer_enablePower                     0x0000139d   Thumb Code    20  ti_msp_dl_config.o(.text.DL_Timer_enablePower)
    [Anonymous Symbol]                       0x0000139c   Section        0  ti_msp_dl_config.o(.text.DL_Timer_enablePower)
    __arm_cp.21_0                            0x000013b0   Number         4  ti_msp_dl_config.o(.text.DL_Timer_enablePower)
    DL_Timer_getPendingInterrupt             0x000013b5   Thumb Code    18  empty.o(.text.DL_Timer_getPendingInterrupt)
    [Anonymous Symbol]                       0x000013b4   Section        0  empty.o(.text.DL_Timer_getPendingInterrupt)
    DL_Timer_getPendingInterrupt             0x000013c7   Thumb Code    18  hw_timer.o(.text.DL_Timer_getPendingInterrupt)
    [Anonymous Symbol]                       0x000013c6   Section        0  hw_timer.o(.text.DL_Timer_getPendingInterrupt)
    [Anonymous Symbol]                       0x000013d8   Section        0  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_1                            0x000014cc   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_2                            0x000014d0   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_4                            0x000014d4   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_5                            0x000014d8   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_6                            0x000014dc   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    [Anonymous Symbol]                       0x000014e0   Section        0  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_0                             0x000015c0   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_2                             0x000015c4   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_3                             0x000015c8   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    DL_Timer_reset                           0x000015cd   Thumb Code    16  ti_msp_dl_config.o(.text.DL_Timer_reset)
    [Anonymous Symbol]                       0x000015cc   Section        0  ti_msp_dl_config.o(.text.DL_Timer_reset)
    DL_Timer_setCCPDirection                 0x000015dd   Thumb Code    20  ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection)
    [Anonymous Symbol]                       0x000015dc   Section        0  ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection)
    [Anonymous Symbol]                       0x000015f0   Section        0  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    __arm_cp.23_0                            0x00001608   Number         4  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    [Anonymous Symbol]                       0x0000160c   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    __arm_cp.27_0                            0x00001620   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    [Anonymous Symbol]                       0x00001624   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    __arm_cp.3_0                             0x00001630   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    [Anonymous Symbol]                       0x00001634   Section        0  dl_timer.o(.text.DL_Timer_setClockConfig)
    DL_Timer_setCounterControl               0x0000164d   Thumb Code    52  ti_msp_dl_config.o(.text.DL_Timer_setCounterControl)
    [Anonymous Symbol]                       0x0000164c   Section        0  ti_msp_dl_config.o(.text.DL_Timer_setCounterControl)
    __arm_cp.48_0                            0x00001680   Number         4  ti_msp_dl_config.o(.text.DL_Timer_setCounterControl)
    __arm_cp.48_1                            0x00001684   Number         4  ti_msp_dl_config.o(.text.DL_Timer_setCounterControl)
    DL_UART_enable                           0x00001689   Thumb Code    22  ti_msp_dl_config.o(.text.DL_UART_enable)
    [Anonymous Symbol]                       0x00001688   Section        0  ti_msp_dl_config.o(.text.DL_UART_enable)
    DL_UART_enableFIFOs                      0x0000169f   Thumb Code    24  ti_msp_dl_config.o(.text.DL_UART_enableFIFOs)
    [Anonymous Symbol]                       0x0000169e   Section        0  ti_msp_dl_config.o(.text.DL_UART_enableFIFOs)
    DL_UART_enableInterrupt                  0x000016b9   Thumb Code    24  ti_msp_dl_config.o(.text.DL_UART_enableInterrupt)
    [Anonymous Symbol]                       0x000016b8   Section        0  ti_msp_dl_config.o(.text.DL_UART_enableInterrupt)
    __arm_cp.62_0                            0x000016d0   Number         4  ti_msp_dl_config.o(.text.DL_UART_enableInterrupt)
    DL_UART_enablePower                      0x000016d5   Thumb Code    20  ti_msp_dl_config.o(.text.DL_UART_enablePower)
    [Anonymous Symbol]                       0x000016d4   Section        0  ti_msp_dl_config.o(.text.DL_UART_enablePower)
    __arm_cp.23_0                            0x000016e8   Number         4  ti_msp_dl_config.o(.text.DL_UART_enablePower)
    DL_UART_getPendingInterrupt              0x000016ed   Thumb Code    18  usart.o(.text.DL_UART_getPendingInterrupt)
    [Anonymous Symbol]                       0x000016ec   Section        0  usart.o(.text.DL_UART_getPendingInterrupt)
    [Anonymous Symbol]                       0x00001700   Section        0  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_0                             0x00001740   Number         4  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_1                             0x00001744   Number         4  dl_uart.o(.text.DL_UART_init)
    DL_UART_isBusy                           0x00001749   Thumb Code    20  usart.o(.text.DL_UART_isBusy)
    [Anonymous Symbol]                       0x00001748   Section        0  usart.o(.text.DL_UART_isBusy)
    DL_UART_reset                            0x0000175d   Thumb Code    16  ti_msp_dl_config.o(.text.DL_UART_reset)
    [Anonymous Symbol]                       0x0000175c   Section        0  ti_msp_dl_config.o(.text.DL_UART_reset)
    __arm_cp.18_0                            0x0000176c   Number         4  ti_msp_dl_config.o(.text.DL_UART_reset)
    __arm_cp.18_1                            0x00001770   Number         4  ti_msp_dl_config.o(.text.DL_UART_reset)
    DL_UART_setBaudRateDivisor               0x00001775   Thumb Code    60  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    [Anonymous Symbol]                       0x00001774   Section        0  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    __arm_cp.61_0                            0x000017b0   Number         4  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    __arm_cp.61_1                            0x000017b4   Number         4  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    __arm_cp.61_2                            0x000017b8   Number         4  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    __arm_cp.61_3                            0x000017bc   Number         4  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    [Anonymous Symbol]                       0x000017c0   Section        0  dl_uart.o(.text.DL_UART_setClockConfig)
    DL_UART_setOversampling                  0x000017d3   Thumb Code    30  ti_msp_dl_config.o(.text.DL_UART_setOversampling)
    [Anonymous Symbol]                       0x000017d2   Section        0  ti_msp_dl_config.o(.text.DL_UART_setOversampling)
    DL_UART_setRXFIFOThreshold               0x000017f1   Thumb Code    36  ti_msp_dl_config.o(.text.DL_UART_setRXFIFOThreshold)
    [Anonymous Symbol]                       0x000017f0   Section        0  ti_msp_dl_config.o(.text.DL_UART_setRXFIFOThreshold)
    DL_UART_setTXFIFOThreshold               0x00001815   Thumb Code    36  ti_msp_dl_config.o(.text.DL_UART_setTXFIFOThreshold)
    [Anonymous Symbol]                       0x00001814   Section        0  ti_msp_dl_config.o(.text.DL_UART_setTXFIFOThreshold)
    __arm_cp.65_0                            0x00001838   Number         4  ti_msp_dl_config.o(.text.DL_UART_setTXFIFOThreshold)
    DL_UART_transmitData                     0x0000183d   Thumb Code    22  usart.o(.text.DL_UART_transmitData)
    [Anonymous Symbol]                       0x0000183c   Section        0  usart.o(.text.DL_UART_transmitData)
    [Anonymous Symbol]                       0x00001854   Section        0  dl_uart.o(.text.DL_UART_transmitDataCheck)
    __arm_cp.9_0                             0x00001868   Number         4  dl_uart.o(.text.DL_UART_transmitDataCheck)
    [Anonymous Symbol]                       0x0000186c   Section        0  bsp_motor_hallencoder.o(.text.GROUP1_IRQHandler)
    __arm_cp.9_1                             0x00001990   Number         4  bsp_motor_hallencoder.o(.text.GROUP1_IRQHandler)
    __arm_cp.9_2                             0x00001994   Number         4  bsp_motor_hallencoder.o(.text.GROUP1_IRQHandler)
    __arm_cp.9_3                             0x00001998   Number         4  bsp_motor_hallencoder.o(.text.GROUP1_IRQHandler)
    [Anonymous Symbol]                       0x0000199c   Section        0  hardware_iic.o(.text.IIC_Get_Digtal)
    [Anonymous Symbol]                       0x000019b4   Section        0  hardware_iic.o(.text.IIC_ReadByte)
    [Anonymous Symbol]                       0x000019d0   Section        0  filter.o(.text.Kalman_Init)
    [Anonymous Symbol]                       0x000019fa   Section        0  filter.o(.text.Kalman_Update)
    [Anonymous Symbol]                       0x00001a66   Section        0  hw_lcd.o(.text.LCD_WR_DATA8)
    [Anonymous Symbol]                       0x00001a7c   Section        0  hw_lcd.o(.text.LCD_WR_REG)
    [Anonymous Symbol]                       0x00001aa8   Section        0  hw_lcd.o(.text.LCD_Writ_Bus)
    __arm_cp.3_0                             0x00001ad4   Number         4  hw_lcd.o(.text.LCD_Writ_Bus)
    [Anonymous Symbol]                       0x00001ad8   Section        0  pid.o(.text.PID_Calculate)
    __arm_cp.1_0                             0x00001b8c   Number         4  pid.o(.text.PID_Calculate)
    [Anonymous Symbol]                       0x00001b90   Section        0  pid.o(.text.PID_Init)
    [Anonymous Symbol]                       0x00001bbc   Section        0  empty.o(.text.PID_operation)
    __arm_cp.3_0                             0x00001ce0   Number         4  empty.o(.text.PID_operation)
    __arm_cp.3_1                             0x00001ce4   Number         4  empty.o(.text.PID_operation)
    __arm_cp.3_2                             0x00001ce8   Number         4  empty.o(.text.PID_operation)
    __arm_cp.3_3                             0x00001cec   Number         4  empty.o(.text.PID_operation)
    __arm_cp.3_4                             0x00001cf0   Number         4  empty.o(.text.PID_operation)
    __arm_cp.3_5                             0x00001cf4   Number         4  empty.o(.text.PID_operation)
    __arm_cp.3_6                             0x00001cf8   Number         4  empty.o(.text.PID_operation)
    __arm_cp.3_7                             0x00001cfc   Number         4  empty.o(.text.PID_operation)
    __arm_cp.3_8                             0x00001d00   Number         4  empty.o(.text.PID_operation)
    __arm_cp.3_9                             0x00001d04   Number         4  empty.o(.text.PID_operation)
    __arm_cp.3_10                            0x00001d08   Number         4  empty.o(.text.PID_operation)
    __arm_cp.3_11                            0x00001d0c   Number         4  empty.o(.text.PID_operation)
    __arm_cp.3_12                            0x00001d10   Number         4  empty.o(.text.PID_operation)
    __arm_cp.3_13                            0x00001d14   Number         4  empty.o(.text.PID_operation)
    __arm_cp.3_14                            0x00001d18   Number         4  empty.o(.text.PID_operation)
    [Anonymous Symbol]                       0x00001d1c   Section        0  hardware_iic.o(.text.Ping)
    [Anonymous Symbol]                       0x00001d50   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init)
    __arm_cp.70_1                            0x00001d78   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init)
    [Anonymous Symbol]                       0x00001d7c   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init)
    __arm_cp.11_0                            0x00001d8c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init)
    [Anonymous Symbol]                       0x00001d90   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_0                             0x00001f58   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_1                             0x00001f5c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_2                             0x00001f60   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_3                             0x00001f64   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_4                             0x00001f68   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_5                             0x00001f6c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_6                             0x00001f70   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    [Anonymous Symbol]                       0x00001f74   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init)
    __arm_cp.8_0                             0x00001fc4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init)
    [Anonymous Symbol]                       0x00001fc8   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    __arm_cp.5_0                             0x0000204c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    __arm_cp.5_2                             0x00002050   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    [Anonymous Symbol]                       0x00002054   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init)
    __arm_cp.4_0                             0x000020b4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init)
    __arm_cp.4_2                             0x000020b8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init)
    [Anonymous Symbol]                       0x000020bc   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init)
    __arm_cp.10_0                            0x000020f0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init)
    __arm_cp.10_2                            0x000020f4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init)
    [Anonymous Symbol]                       0x000020f8   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_0                             0x0000214c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    [Anonymous Symbol]                       0x00002150   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    [Anonymous Symbol]                       0x00002160   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.6_0                             0x00002188   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.6_2                             0x0000218c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    [Anonymous Symbol]                       0x00002190   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init)
    __arm_cp.7_0                             0x000021c0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init)
    __arm_cp.7_2                             0x000021c4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init)
    [Anonymous Symbol]                       0x000021c8   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.9_0                             0x00002224   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.9_2                             0x00002228   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    [Anonymous Symbol]                       0x0000222c   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_0                             0x00002278   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_1                             0x0000227c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_2                             0x00002280   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_3                             0x00002284   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    [Anonymous Symbol]                       0x00002288   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_0                             0x00002314   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_1                             0x00002318   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_2                             0x0000231c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_3                             0x00002320   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_6                             0x00002324   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_8                             0x00002328   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    [Anonymous Symbol]                       0x0000232c   Section        0  hw_timer.o(.text.TIMA0_IRQHandler)
    __arm_cp.3_0                             0x00002348   Number         4  hw_timer.o(.text.TIMA0_IRQHandler)
    [Anonymous Symbol]                       0x0000234c   Section        0  empty.o(.text.TIMG0_IRQHandler)
    __arm_cp.4_0                             0x000023e0   Number         4  empty.o(.text.TIMG0_IRQHandler)
    __arm_cp.4_1                             0x000023e4   Number         4  empty.o(.text.TIMG0_IRQHandler)
    __arm_cp.4_2                             0x000023e8   Number         4  empty.o(.text.TIMG0_IRQHandler)
    __arm_cp.4_3                             0x000023ec   Number         4  empty.o(.text.TIMG0_IRQHandler)
    __arm_cp.4_4                             0x000023f0   Number         4  empty.o(.text.TIMG0_IRQHandler)
    __arm_cp.4_5                             0x000023f4   Number         4  empty.o(.text.TIMG0_IRQHandler)
    __arm_cp.4_6                             0x000023f8   Number         4  empty.o(.text.TIMG0_IRQHandler)
    __arm_cp.4_7                             0x000023fc   Number         4  empty.o(.text.TIMG0_IRQHandler)
    __arm_cp.4_8                             0x00002400   Number         4  empty.o(.text.TIMG0_IRQHandler)
    __arm_cp.4_9                             0x00002404   Number         4  empty.o(.text.TIMG0_IRQHandler)
    __arm_cp.4_10                            0x00002408   Number         4  empty.o(.text.TIMG0_IRQHandler)
    [Anonymous Symbol]                       0x0000240c   Section        0  usart.o(.text.UART0_IRQHandler)
    __arm_cp.6_0                             0x00002440   Number         4  usart.o(.text.UART0_IRQHandler)
    __arm_cp.6_1                             0x00002444   Number         4  usart.o(.text.UART0_IRQHandler)
    __arm_cp.6_2                             0x00002448   Number         4  usart.o(.text.UART0_IRQHandler)
    [Anonymous Symbol]                       0x0000244c   Section        0  sensor.o(.text.UpdatePosition)
    __NVIC_ClearPendingIRQ                   0x00002469   Thumb Code    40  empty.o(.text.__NVIC_ClearPendingIRQ)
    [Anonymous Symbol]                       0x00002468   Section        0  empty.o(.text.__NVIC_ClearPendingIRQ)
    __NVIC_ClearPendingIRQ                   0x00002491   Thumb Code    40  bsp_motor_hallencoder.o(.text.__NVIC_ClearPendingIRQ)
    [Anonymous Symbol]                       0x00002490   Section        0  bsp_motor_hallencoder.o(.text.__NVIC_ClearPendingIRQ)
    __NVIC_ClearPendingIRQ                   0x000024b9   Thumb Code    40  hw_timer.o(.text.__NVIC_ClearPendingIRQ)
    [Anonymous Symbol]                       0x000024b8   Section        0  hw_timer.o(.text.__NVIC_ClearPendingIRQ)
    __arm_cp.1_0                             0x000024e0   Number         4  hw_timer.o(.text.__NVIC_ClearPendingIRQ)
    __NVIC_EnableIRQ                         0x000024e5   Thumb Code    40  empty.o(.text.__NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x000024e4   Section        0  empty.o(.text.__NVIC_EnableIRQ)
    __NVIC_EnableIRQ                         0x0000250d   Thumb Code    40  bsp_motor_hallencoder.o(.text.__NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x0000250c   Section        0  bsp_motor_hallencoder.o(.text.__NVIC_EnableIRQ)
    __NVIC_EnableIRQ                         0x00002535   Thumb Code    40  hw_timer.o(.text.__NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x00002534   Section        0  hw_timer.o(.text.__NVIC_EnableIRQ)
    __arm_cp.2_0                             0x0000255c   Number         4  hw_timer.o(.text.__NVIC_EnableIRQ)
    __NVIC_SetPriority                       0x00002561   Thumb Code   124  ti_msp_dl_config.o(.text.__NVIC_SetPriority)
    [Anonymous Symbol]                       0x00002560   Section        0  ti_msp_dl_config.o(.text.__NVIC_SetPriority)
    __arm_cp.47_0                            0x000025dc   Number         4  ti_msp_dl_config.o(.text.__NVIC_SetPriority)
    __arm_cp.47_1                            0x000025e0   Number         4  ti_msp_dl_config.o(.text.__NVIC_SetPriority)
    [Anonymous Symbol]                       0x000025e4   Section        0  time.o(.text.delay_ms)
    [Anonymous Symbol]                       0x000025fc   Section        0  time.o(.text.delay_us)
    __arm_cp.0_0                             0x00002668   Number         4  time.o(.text.delay_us)
    __arm_cp.0_1                             0x0000266c   Number         4  time.o(.text.delay_us)
    [Anonymous Symbol]                       0x00002670   Section        0  bsp_motor_hallencoder.o(.text.encoder_init)
    [Anonymous Symbol]                       0x00002688   Section        0  bsp_motor_hallencoder.o(.text.encoder_update_L)
    [Anonymous Symbol]                       0x000026a0   Section        0  bsp_motor_hallencoder.o(.text.encoder_update_R)
    [Anonymous Symbol]                       0x000026b8   Section        0  bsp_motor_hallencoder.o(.text.get_encoder_count_L)
    __arm_cp.3_0                             0x000026d0   Number         4  bsp_motor_hallencoder.o(.text.get_encoder_count_L)
    [Anonymous Symbol]                       0x000026d4   Section        0  bsp_motor_hallencoder.o(.text.get_encoder_count_R)
    __arm_cp.4_0                             0x000026ec   Number         4  bsp_motor_hallencoder.o(.text.get_encoder_count_R)
    __arm_cp.4_1                             0x000026f0   Number         4  bsp_motor_hallencoder.o(.text.get_encoder_count_R)
    [Anonymous Symbol]                       0x000026f4   Section        0  hardware_iic.o(.text.grayscale_detection_sensor)
    __arm_cp.9_0                             0x00002760   Number         4  hardware_iic.o(.text.grayscale_detection_sensor)
    __arm_cp.9_1                             0x00002764   Number         4  hardware_iic.o(.text.grayscale_detection_sensor)
    __arm_cp.9_2                             0x00002768   Number         4  hardware_iic.o(.text.grayscale_detection_sensor)
    __arm_cp.9_3                             0x0000276c   Number         4  hardware_iic.o(.text.grayscale_detection_sensor)
    [Anonymous Symbol]                       0x00002770   Section        0  hw_i2c.o(.text.hardware_IIC_ReadByte)
    __arm_cp.4_0                             0x0000281c   Number         4  hw_i2c.o(.text.hardware_IIC_ReadByte)
    [Anonymous Symbol]                       0x00002820   Section        0  hw_lcd.o(.text.lcd_init)
    __arm_cp.10_0                            0x000029e0   Number         4  hw_lcd.o(.text.lcd_init)
    __arm_cp.10_1                            0x000029e4   Number         4  hw_lcd.o(.text.lcd_init)
    __arm_cp.10_2                            0x000029e8   Number         4  hw_lcd.o(.text.lcd_init)
    __arm_cp.10_3                            0x000029ec   Number         4  hw_lcd.o(.text.lcd_init)
    [Anonymous Symbol]                       0x000029f0   Section        0  empty.o(.text.main)
    __arm_cp.0_0                             0x00002ab0   Number         4  empty.o(.text.main)
    __arm_cp.0_1                             0x00002ab4   Number         4  empty.o(.text.main)
    __arm_cp.0_2                             0x00002ab8   Number         4  empty.o(.text.main)
    __arm_cp.0_3                             0x00002abc   Number         4  empty.o(.text.main)
    __arm_cp.0_4                             0x00002ac0   Number         4  empty.o(.text.main)
    __arm_cp.0_5                             0x00002ac4   Number         4  empty.o(.text.main)
    __arm_cp.0_6                             0x00002ac8   Number         4  empty.o(.text.main)
    __arm_cp.0_7                             0x00002acc   Number         4  empty.o(.text.main)
    __arm_cp.0_8                             0x00002ad0   Number         4  empty.o(.text.main)
    __arm_cp.0_9                             0x00002ad4   Number         4  empty.o(.text.main)
    __arm_cp.0_10                            0x00002ad8   Number         4  empty.o(.text.main)
    __arm_cp.0_11                            0x00002adc   Number         4  empty.o(.text.main)
    [Anonymous Symbol]                       0x00002ae0   Section        0  hw_lcd.o(.text.spi_write_bus)
    __arm_cp.0_0                             0x00002b08   Number         4  hw_lcd.o(.text.spi_write_bus)
    [Anonymous Symbol]                       0x00002b0c   Section        0  hw_timer.o(.text.timer_init)
    [Anonymous Symbol]                       0x00002b24   Section        0  usart.o(.text.uart0_send_char)
    [Anonymous Symbol]                       0x00002b4c   Section        0  usart.o(.text.uart0_send_string)
    [Anonymous Symbol]                       0x00002b88   Section        0  usart.o(.text.uart_send)
    __arm_cp.5_0                             0x00002bec   Number         4  usart.o(.text.uart_send)
    __arm_cp.5_1                             0x00002bf0   Number         4  usart.o(.text.uart_send)
    __arm_cp.5_2                             0x00002bf4   Number         4  usart.o(.text.uart_send)
    __arm_cp.5_3                             0x00002bf8   Number         4  usart.o(.text.uart_send)
    i.__0sprintf                             0x00002bfc   Section        0  printfa.o(i.__0sprintf)
    i.__ARM_clz                              0x00002c24   Section        0  depilogue.o(i.__ARM_clz)
    i.__scatterload_copy                     0x00002c58   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x00002c68   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x00002c70   Section       14  handlers.o(i.__scatterload_zeroinit)
    _fp_digits                               0x00002c81   Thumb Code   344  printfa.o(i._fp_digits)
    i._fp_digits                             0x00002c80   Section        0  printfa.o(i._fp_digits)
    _printf_core                             0x00002df5   Thumb Code  1754  printfa.o(i._printf_core)
    i._printf_core                           0x00002df4   Section        0  printfa.o(i._printf_core)
    _printf_post_padding                     0x000034e1   Thumb Code    32  printfa.o(i._printf_post_padding)
    i._printf_post_padding                   0x000034e0   Section        0  printfa.o(i._printf_post_padding)
    _printf_pre_padding                      0x00003501   Thumb Code    44  printfa.o(i._printf_pre_padding)
    i._printf_pre_padding                    0x00003500   Section        0  printfa.o(i._printf_pre_padding)
    _sputc                                   0x0000352d   Thumb Code    10  printfa.o(i._sputc)
    i._sputc                                 0x0000352c   Section        0  printfa.o(i._sputc)
    gDMA_CH0Config                           0x00003538   Data          24  ti_msp_dl_config.o(.rodata.gDMA_CH0Config)
    [Anonymous Symbol]                       0x00003538   Section        0  ti_msp_dl_config.o(.rodata.gDMA_CH0Config)
    gI2C_0ClockConfig                        0x00003550   Data           2  ti_msp_dl_config.o(.rodata.gI2C_0ClockConfig)
    [Anonymous Symbol]                       0x00003550   Section        0  ti_msp_dl_config.o(.rodata.gI2C_0ClockConfig)
    gPWM_0ClockConfig                        0x00003552   Data           3  ti_msp_dl_config.o(.rodata.gPWM_0ClockConfig)
    [Anonymous Symbol]                       0x00003552   Section        0  ti_msp_dl_config.o(.rodata.gPWM_0ClockConfig)
    gPWM_0Config                             0x00003558   Data           8  ti_msp_dl_config.o(.rodata.gPWM_0Config)
    [Anonymous Symbol]                       0x00003558   Section        0  ti_msp_dl_config.o(.rodata.gPWM_0Config)
    gPWM_LEDClockConfig                      0x00003560   Data           3  ti_msp_dl_config.o(.rodata.gPWM_LEDClockConfig)
    [Anonymous Symbol]                       0x00003560   Section        0  ti_msp_dl_config.o(.rodata.gPWM_LEDClockConfig)
    gPWM_LEDConfig                           0x00003564   Data           8  ti_msp_dl_config.o(.rodata.gPWM_LEDConfig)
    [Anonymous Symbol]                       0x00003564   Section        0  ti_msp_dl_config.o(.rodata.gPWM_LEDConfig)
    gSPI_LCD_clockConfig                     0x0000356c   Data           2  ti_msp_dl_config.o(.rodata.gSPI_LCD_clockConfig)
    [Anonymous Symbol]                       0x0000356c   Section        0  ti_msp_dl_config.o(.rodata.gSPI_LCD_clockConfig)
    gSPI_LCD_config                          0x0000356e   Data          10  ti_msp_dl_config.o(.rodata.gSPI_LCD_config)
    [Anonymous Symbol]                       0x0000356e   Section        0  ti_msp_dl_config.o(.rodata.gSPI_LCD_config)
    gSYSPLLConfig                            0x00003578   Data          40  ti_msp_dl_config.o(.rodata.gSYSPLLConfig)
    [Anonymous Symbol]                       0x00003578   Section        0  ti_msp_dl_config.o(.rodata.gSYSPLLConfig)
    gTIMER_0ClockConfig                      0x000035a0   Data           3  ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig)
    [Anonymous Symbol]                       0x000035a0   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig)
    gTIMER_0TimerConfig                      0x000035a4   Data          20  ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig)
    [Anonymous Symbol]                       0x000035a4   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig)
    gTIMER_TICKClockConfig                   0x000035b8   Data           3  ti_msp_dl_config.o(.rodata.gTIMER_TICKClockConfig)
    [Anonymous Symbol]                       0x000035b8   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_TICKClockConfig)
    gTIMER_TICKTimerConfig                   0x000035bc   Data          20  ti_msp_dl_config.o(.rodata.gTIMER_TICKTimerConfig)
    [Anonymous Symbol]                       0x000035bc   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_TICKTimerConfig)
    gUART_0ClockConfig                       0x000035d0   Data           2  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    [Anonymous Symbol]                       0x000035d0   Section        0  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    gUART_0Config                            0x000035d2   Data          10  ti_msp_dl_config.o(.rodata.gUART_0Config)
    [Anonymous Symbol]                       0x000035d2   Section        0  ti_msp_dl_config.o(.rodata.gUART_0Config)
    position_weights                         0x000035dc   Data          32  sensor.o(.rodata.position_weights)
    [Anonymous Symbol]                       0x000035dc   Section        0  sensor.o(.rodata.position_weights)
    [Anonymous Symbol]                       0x000035fc   Section        0  empty.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x0000360c   Section        0  hardware_iic.o(.rodata.str1.1)
    TIMG0_IRQHandler.timer_count             0x20200000   Data           4  empty.o(.data.TIMG0_IRQHandler.timer_count)
    [Anonymous Symbol]                       0x20200000   Section        0  empty.o(.data.TIMG0_IRQHandler.timer_count)
    PID_operation.total_left_speed           0x20200014   Data           4  empty.o(.bss.PID_operation.total_left_speed)
    [Anonymous Symbol]                       0x20200014   Section        0  empty.o(.bss.PID_operation.total_left_speed)
    PID_operation.total_right_speed          0x20200018   Data           4  empty.o(.bss.PID_operation.total_right_speed)
    [Anonymous Symbol]                       0x20200018   Section        0  empty.o(.bss.PID_operation.total_right_speed)
    PID_operation.total_speed                0x2020001c   Data           4  empty.o(.bss.PID_operation.total_speed)
    [Anonymous Symbol]                       0x2020001c   Section        0  empty.o(.bss.PID_operation.total_speed)
    motor_encoder_L                          0x20200278   Data          16  bsp_motor_hallencoder.o(.bss.motor_encoder_L)
    [Anonymous Symbol]                       0x20200278   Section        0  bsp_motor_hallencoder.o(.bss.motor_encoder_L)
    motor_encoder_R                          0x20200288   Data          16  bsp_motor_hallencoder.o(.bss.motor_encoder_R)
    [Anonymous Symbol]                       0x20200288   Section        0  bsp_motor_hallencoder.o(.bss.motor_encoder_R)
    STACK                                    0x202005e8   Section     1024  startup_mspm0g350x_uvision.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv3M$S$PE$A:L22$X:L11$S22$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEJ$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __Vectors                                0x00000000   Data           4  startup_mspm0g350x_uvision.o(RESET)
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_End                            0x000000c0   Data           0  startup_mspm0g350x_uvision.o(RESET)
    __Vectors_Size                           0x000000c0   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    __main                                   0x000000c1   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x000000c1   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x000000c5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x000000c9   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x000000c9   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x000000c9   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x000000c9   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x000000d1   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x000000d1   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x000000d5   Thumb Code     4  startup_mspm0g350x_uvision.o(.text)
    NMI_Handler                              0x000000d9   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    HardFault_Handler                        0x000000db   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    SVC_Handler                              0x000000dd   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    PendSV_Handler                           0x000000df   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    SysTick_Handler                          0x000000e1   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    ADC0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    ADC1_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    AES_IRQHandler                           0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    CANFD0_IRQHandler                        0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DAC0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DMA_IRQHandler                           0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    Default_Handler                          0x000000e3   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    GROUP0_IRQHandler                        0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C1_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    RTC_IRQHandler                           0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI1_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA1_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG12_IRQHandler                        0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG6_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG7_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG8_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART1_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART2_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART3_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    __aeabi_memset                           0x000000e9   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x000000e9   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x000000e9   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x000000f7   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x000000f7   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x000000f7   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x000000fb   Thumb Code    18  memseta.o(.text)
    strlen                                   0x0000010d   Thumb Code    14  strlen.o(.text)
    __aeabi_fadd                             0x0000011b   Thumb Code   162  fadd.o(.text)
    __aeabi_fsub                             0x000001bd   Thumb Code     8  fadd.o(.text)
    __aeabi_frsub                            0x000001c5   Thumb Code     8  fadd.o(.text)
    __aeabi_fmul                             0x000001cd   Thumb Code   122  fmul.o(.text)
    __aeabi_fdiv                             0x00000247   Thumb Code   124  fdiv.o(.text)
    __aeabi_dadd                             0x000002c5   Thumb Code   328  dadd.o(.text)
    __aeabi_dsub                             0x0000040d   Thumb Code    12  dadd.o(.text)
    __aeabi_drsub                            0x00000419   Thumb Code    12  dadd.o(.text)
    __aeabi_dmul                             0x00000429   Thumb Code   202  dmul.o(.text)
    __aeabi_ddiv                             0x000004f9   Thumb Code   234  ddiv.o(.text)
    __aeabi_fcmple                           0x000005e9   Thumb Code    28  fcmple.o(.text)
    __aeabi_i2f                              0x00000605   Thumb Code    22  fflti.o(.text)
    __aeabi_i2d                              0x0000061d   Thumb Code    34  dflti.o(.text)
    __aeabi_f2iz                             0x00000645   Thumb Code    50  ffixi.o(.text)
    __aeabi_d2iz                             0x00000679   Thumb Code    62  dfixi.o(.text)
    __aeabi_f2d                              0x000006c1   Thumb Code    40  f2d.o(.text)
    __aeabi_uidiv                            0x000006e9   Thumb Code     0  uidiv_div0.o(.text)
    __aeabi_uidivmod                         0x000006e9   Thumb Code    62  uidiv_div0.o(.text)
    __aeabi_uldivmod                         0x00000727   Thumb Code    96  uldiv.o(.text)
    __aeabi_llsl                             0x00000787   Thumb Code    32  llshl.o(.text)
    _ll_shift_l                              0x00000787   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x000007a7   Thumb Code    34  llushr.o(.text)
    _ll_ushift_r                             0x000007a7   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x000007c9   Thumb Code    38  llsshr.o(.text)
    _ll_sshift_r                             0x000007c9   Thumb Code     0  llsshr.o(.text)
    __I$use$fp                               0x000007ef   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x000007ef   Thumb Code    16  fepilogue.o(.text)
    _float_epilogue                          0x000007ff   Thumb Code   114  fepilogue.o(.text)
    _double_round                            0x00000871   Thumb Code    26  depilogue.o(.text)
    _double_epilogue                         0x0000088b   Thumb Code   164  depilogue.o(.text)
    __aeabi_d2ulz                            0x00000931   Thumb Code    54  dfixul.o(.text)
    __aeabi_cdrcmple                         0x00000971   Thumb Code    38  cdrcmple.o(.text)
    __scatterload                            0x00000999   Thumb Code    38  init.o(.text)
    __scatterload_rt2                        0x00000999   Thumb Code     0  init.o(.text)
    AB_Control                               0x000009c9   Thumb Code   168  bsp_tb6612.o(.text.AB_Control)
    CalculatePositionFromDigital             0x00000a79   Thumb Code   124  sensor.o(.text.CalculatePositionFromDigital)
    DL_Common_delayCycles                    0x00000af9   Thumb Code    10  dl_common.o(.text.DL_Common_delayCycles)
    DL_DMA_initChannel                       0x00000b81   Thumb Code    68  dl_dma.o(.text.DL_DMA_initChannel)
    DL_I2C_fillControllerTXFIFO              0x00000eb5   Thumb Code    44  dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO)
    DL_I2C_flushControllerTXFIFO             0x00000ee1   Thumb Code    36  dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO)
    DL_I2C_setClockConfig                    0x00000f73   Thumb Code    38  dl_i2c.o(.text.DL_I2C_setClockConfig)
    DL_SPI_init                              0x0000107d   Thumb Code    68  dl_spi.o(.text.DL_SPI_init)
    DL_SPI_setClockConfig                    0x00001111   Thumb Code    18  dl_spi.o(.text.DL_SPI_setClockConfig)
    DL_SYSCTL_configSYSPLL                   0x0000116d   Thumb Code   192  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    DL_SYSCTL_setHFCLKSourceHFXTParams       0x00001291   Thumb Code    80  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
    DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK    0x00001315   Thumb Code    40  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    DL_Timer_initFourCCPWMMode               0x000013d9   Thumb Code   264  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    DL_Timer_initTimerMode                   0x000014e1   Thumb Code   236  dl_timer.o(.text.DL_Timer_initTimerMode)
    DL_Timer_setCaptCompUpdateMethod         0x000015f1   Thumb Code    28  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    DL_Timer_setCaptureCompareOutCtl         0x0000160d   Thumb Code    24  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    DL_Timer_setCaptureCompareValue          0x00001625   Thumb Code    16  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    DL_Timer_setClockConfig                  0x00001635   Thumb Code    24  dl_timer.o(.text.DL_Timer_setClockConfig)
    DL_UART_init                             0x00001701   Thumb Code    72  dl_uart.o(.text.DL_UART_init)
    DL_UART_setClockConfig                   0x000017c1   Thumb Code    18  dl_uart.o(.text.DL_UART_setClockConfig)
    DL_UART_transmitDataCheck                0x00001855   Thumb Code    24  dl_uart.o(.text.DL_UART_transmitDataCheck)
    GROUP1_IRQHandler                        0x0000186d   Thumb Code   292  bsp_motor_hallencoder.o(.text.GROUP1_IRQHandler)
    IIC_Get_Digtal                           0x0000199d   Thumb Code    24  hardware_iic.o(.text.IIC_Get_Digtal)
    IIC_ReadByte                             0x000019b5   Thumb Code    28  hardware_iic.o(.text.IIC_ReadByte)
    Kalman_Init                              0x000019d1   Thumb Code    42  filter.o(.text.Kalman_Init)
    Kalman_Update                            0x000019fb   Thumb Code   108  filter.o(.text.Kalman_Update)
    LCD_WR_DATA8                             0x00001a67   Thumb Code    20  hw_lcd.o(.text.LCD_WR_DATA8)
    LCD_WR_REG                               0x00001a7d   Thumb Code    44  hw_lcd.o(.text.LCD_WR_REG)
    LCD_Writ_Bus                             0x00001aa9   Thumb Code    44  hw_lcd.o(.text.LCD_Writ_Bus)
    PID_Calculate                            0x00001ad9   Thumb Code   180  pid.o(.text.PID_Calculate)
    PID_Init                                 0x00001b91   Thumb Code    44  pid.o(.text.PID_Init)
    PID_operation                            0x00001bbd   Thumb Code   292  empty.o(.text.PID_operation)
    Ping                                     0x00001d1d   Thumb Code    50  hardware_iic.o(.text.Ping)
    SYSCFG_DL_DMA_CH0_init                   0x00001d51   Thumb Code    40  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init)
    SYSCFG_DL_DMA_init                       0x00001d7d   Thumb Code    16  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init)
    SYSCFG_DL_GPIO_init                      0x00001d91   Thumb Code   456  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    SYSCFG_DL_I2C_0_init                     0x00001f75   Thumb Code    80  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init)
    SYSCFG_DL_PWM_0_init                     0x00001fc9   Thumb Code   132  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    SYSCFG_DL_PWM_LED_init                   0x00002055   Thumb Code    96  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init)
    SYSCFG_DL_SPI_LCD_init                   0x000020bd   Thumb Code    52  ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init)
    SYSCFG_DL_SYSCTL_init                    0x000020f9   Thumb Code    84  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    SYSCFG_DL_SYSTICK_init                   0x00002151   Thumb Code    14  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    SYSCFG_DL_TIMER_0_init                   0x00002161   Thumb Code    40  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    SYSCFG_DL_TIMER_TICK_init                0x00002191   Thumb Code    48  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init)
    SYSCFG_DL_UART_0_init                    0x000021c9   Thumb Code    92  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    SYSCFG_DL_init                           0x0000222d   Thumb Code    76  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    SYSCFG_DL_initPower                      0x00002289   Thumb Code   140  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    TIMA0_IRQHandler                         0x0000232d   Thumb Code    28  hw_timer.o(.text.TIMA0_IRQHandler)
    TIMG0_IRQHandler                         0x0000234d   Thumb Code   148  empty.o(.text.TIMG0_IRQHandler)
    UART0_IRQHandler                         0x0000240d   Thumb Code    52  usart.o(.text.UART0_IRQHandler)
    UpdatePosition                           0x0000244d   Thumb Code    28  sensor.o(.text.UpdatePosition)
    delay_ms                                 0x000025e5   Thumb Code    22  time.o(.text.delay_ms)
    delay_us                                 0x000025fd   Thumb Code   108  time.o(.text.delay_us)
    encoder_init                             0x00002671   Thumb Code    22  bsp_motor_hallencoder.o(.text.encoder_init)
    encoder_update_L                         0x00002689   Thumb Code    24  bsp_motor_hallencoder.o(.text.encoder_update_L)
    encoder_update_R                         0x000026a1   Thumb Code    24  bsp_motor_hallencoder.o(.text.encoder_update_R)
    get_encoder_count_L                      0x000026b9   Thumb Code    24  bsp_motor_hallencoder.o(.text.get_encoder_count_L)
    get_encoder_count_R                      0x000026d5   Thumb Code    24  bsp_motor_hallencoder.o(.text.get_encoder_count_R)
    grayscale_detection_sensor               0x000026f5   Thumb Code   108  hardware_iic.o(.text.grayscale_detection_sensor)
    hardware_IIC_ReadByte                    0x00002771   Thumb Code   172  hw_i2c.o(.text.hardware_IIC_ReadByte)
    lcd_init                                 0x00002821   Thumb Code   448  hw_lcd.o(.text.lcd_init)
    main                                     0x000029f1   Thumb Code   192  empty.o(.text.main)
    spi_write_bus                            0x00002ae1   Thumb Code    40  hw_lcd.o(.text.spi_write_bus)
    timer_init                               0x00002b0d   Thumb Code    22  hw_timer.o(.text.timer_init)
    uart0_send_char                          0x00002b25   Thumb Code    40  usart.o(.text.uart0_send_char)
    uart0_send_string                        0x00002b4d   Thumb Code    60  usart.o(.text.uart0_send_string)
    uart_send                                0x00002b89   Thumb Code   100  usart.o(.text.uart_send)
    __0sprintf                               0x00002bfd   Thumb Code    36  printfa.o(i.__0sprintf)
    __1sprintf                               0x00002bfd   Thumb Code     0  printfa.o(i.__0sprintf)
    __2sprintf                               0x00002bfd   Thumb Code     0  printfa.o(i.__0sprintf)
    __c89sprintf                             0x00002bfd   Thumb Code     0  printfa.o(i.__0sprintf)
    sprintf                                  0x00002bfd   Thumb Code     0  printfa.o(i.__0sprintf)
    __ARM_clz                                0x00002c25   Thumb Code    46  depilogue.o(i.__ARM_clz)
    __scatterload_copy                       0x00002c59   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x00002c69   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x00002c71   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    Region$$Table$$Base                      0x00003648   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x00003668   Number         0  anon$$obj.o(Region$$Table)
    uart_tx_dma_complete_flag                0x20200004   Data           1  usart.o(.data.uart_tx_dma_complete_flag)
    Num_L                                    0x20200008   Data           4  hw_timer.o(.bss.Num_L)
    Num_R                                    0x2020000c   Data           4  hw_timer.o(.bss.Num_R)
    PID_flash_time                           0x20200010   Data           1  empty.o(.bss.PID_flash_time)
    bmq_flash_time                           0x20200020   Data           1  empty.o(.bss.bmq_flash_time)
    gPWM_0Backup                             0x20200024   Data         160  ti_msp_dl_config.o(.bss.gPWM_0Backup)
    gPWM_LEDBackup                           0x202000c4   Data         160  ti_msp_dl_config.o(.bss.gPWM_LEDBackup)
    gSPI_LCDBackup                           0x20200164   Data          40  ti_msp_dl_config.o(.bss.gSPI_LCDBackup)
    gTIMER_TICKBackup                        0x2020018c   Data         188  ti_msp_dl_config.o(.bss.gTIMER_TICKBackup)
    has_send_len                             0x20200248   Data           2  usart.o(.bss.has_send_len)
    j                                        0x2020024a   Data           2  empty.o(.bss.j)
    led_flash_time                           0x2020024c   Data           1  empty.o(.bss.led_flash_time)
    left_kalman                              0x20200250   Data          16  empty.o(.bss.left_kalman)
    left_speed_pid                           0x20200260   Data          20  empty.o(.bss.left_speed_pid)
    posion_kalman                            0x20200298   Data          16  empty.o(.bss.posion_kalman)
    posion_pid                               0x202002a8   Data          20  empty.o(.bss.posion_pid)
    right_kalman                             0x202002bc   Data          16  empty.o(.bss.right_kalman)
    right_speed_pid                          0x202002cc   Data          20  empty.o(.bss.right_speed_pid)
    rx_buff                                  0x202002e0   Data         256  usart.o(.bss.rx_buff)
    send_len                                 0x202003e0   Data           2  usart.o(.bss.send_len)
    temporary_buffer                         0x202003e2   Data         512  empty.o(.bss.temporary_buffer)
    __initial_sp                             0x202009e8   Data           0  startup_mspm0g350x_uvision.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x000000c1

  Load Region LR_IROM1 (Base: 0x00000000, Size: 0x00003670, Max: 0x00020000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x00000000, Load base: 0x00000000, Size: 0x00003668, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x00000000   0x00000000   0x000000c0   Data   RO           39    RESET               startup_mspm0g350x_uvision.o
    0x000000c0   0x000000c0   0x00000000   Code   RO          880  * .ARM.Collect$$$$00000000  mc_p.l(entry.o)
    0x000000c0   0x000000c0   0x00000004   Code   RO          955    .ARM.Collect$$$$00000001  mc_p.l(entry2.o)
    0x000000c4   0x000000c4   0x00000004   Code   RO          958    .ARM.Collect$$$$00000004  mc_p.l(entry5.o)
    0x000000c8   0x000000c8   0x00000000   Code   RO          960    .ARM.Collect$$$$00000008  mc_p.l(entry7b.o)
    0x000000c8   0x000000c8   0x00000000   Code   RO          962    .ARM.Collect$$$$0000000A  mc_p.l(entry8b.o)
    0x000000c8   0x000000c8   0x00000008   Code   RO          963    .ARM.Collect$$$$0000000B  mc_p.l(entry9a.o)
    0x000000d0   0x000000d0   0x00000000   Code   RO          965    .ARM.Collect$$$$0000000D  mc_p.l(entry10a.o)
    0x000000d0   0x000000d0   0x00000000   Code   RO          967    .ARM.Collect$$$$0000000F  mc_p.l(entry11a.o)
    0x000000d0   0x000000d0   0x00000004   Code   RO          956    .ARM.Collect$$$$00002712  mc_p.l(entry2.o)
    0x000000d4   0x000000d4   0x00000014   Code   RO           40    .text               startup_mspm0g350x_uvision.o
    0x000000e8   0x000000e8   0x00000024   Code   RO          891    .text               mc_p.l(memseta.o)
    0x0000010c   0x0000010c   0x0000000e   Code   RO          893    .text               mc_p.l(strlen.o)
    0x0000011a   0x0000011a   0x000000b2   Code   RO          925    .text               mf_p.l(fadd.o)
    0x000001cc   0x000001cc   0x0000007a   Code   RO          927    .text               mf_p.l(fmul.o)
    0x00000246   0x00000246   0x0000007c   Code   RO          929    .text               mf_p.l(fdiv.o)
    0x000002c2   0x000002c2   0x00000002   PAD
    0x000002c4   0x000002c4   0x00000164   Code   RO          931    .text               mf_p.l(dadd.o)
    0x00000428   0x00000428   0x000000d0   Code   RO          933    .text               mf_p.l(dmul.o)
    0x000004f8   0x000004f8   0x000000f0   Code   RO          935    .text               mf_p.l(ddiv.o)
    0x000005e8   0x000005e8   0x0000001c   Code   RO          937    .text               mf_p.l(fcmple.o)
    0x00000604   0x00000604   0x00000016   Code   RO          939    .text               mf_p.l(fflti.o)
    0x0000061a   0x0000061a   0x00000002   PAD
    0x0000061c   0x0000061c   0x00000028   Code   RO          941    .text               mf_p.l(dflti.o)
    0x00000644   0x00000644   0x00000032   Code   RO          945    .text               mf_p.l(ffixi.o)
    0x00000676   0x00000676   0x00000002   PAD
    0x00000678   0x00000678   0x00000048   Code   RO          949    .text               mf_p.l(dfixi.o)
    0x000006c0   0x000006c0   0x00000028   Code   RO          951    .text               mf_p.l(f2d.o)
    0x000006e8   0x000006e8   0x0000003e   Code   RO          974    .text               mc_p.l(uidiv_div0.o)
    0x00000726   0x00000726   0x00000060   Code   RO          980    .text               mc_p.l(uldiv.o)
    0x00000786   0x00000786   0x00000020   Code   RO          982    .text               mc_p.l(llshl.o)
    0x000007a6   0x000007a6   0x00000022   Code   RO          984    .text               mc_p.l(llushr.o)
    0x000007c8   0x000007c8   0x00000026   Code   RO          986    .text               mc_p.l(llsshr.o)
    0x000007ee   0x000007ee   0x00000000   Code   RO          995    .text               mc_p.l(iusefp.o)
    0x000007ee   0x000007ee   0x00000082   Code   RO          996    .text               mf_p.l(fepilogue.o)
    0x00000870   0x00000870   0x000000be   Code   RO          998    .text               mf_p.l(depilogue.o)
    0x0000092e   0x0000092e   0x00000002   PAD
    0x00000930   0x00000930   0x00000040   Code   RO         1004    .text               mf_p.l(dfixul.o)
    0x00000970   0x00000970   0x00000028   Code   RO         1006    .text               mf_p.l(cdrcmple.o)
    0x00000998   0x00000998   0x00000030   Code   RO         1008    .text               mc_p.l(init.o)
    0x000009c8   0x000009c8   0x000000b0   Code   RO          482    .text.AB_Control    bsp_tb6612.o
    0x00000a78   0x00000a78   0x00000080   Code   RO          587    .text.CalculatePositionFromDigital  sensor.o
    0x00000af8   0x00000af8   0x0000000a   Code   RO          599    .text.DL_Common_delayCycles  driverlib.a(dl_common.o)
    0x00000b02   0x00000b02   0x00000028   Code   RO          199    .text.DL_Common_updateReg  ti_msp_dl_config.o
    0x00000b2a   0x00000b2a   0x00000028   Code   RO          566    .text.DL_Common_updateReg  hw_i2c.o
    0x00000b52   0x00000b52   0x00000002   PAD
    0x00000b54   0x00000b54   0x00000014   Code   RO          189    .text.DL_DMA_clearInterruptStatus  ti_msp_dl_config.o
    0x00000b68   0x00000b68   0x00000018   Code   RO          191    .text.DL_DMA_enableInterrupt  ti_msp_dl_config.o
    0x00000b80   0x00000b80   0x00000044   Code   RO          608    .text.DL_DMA_initChannel  driverlib.a(dl_dma.o)
    0x00000bc4   0x00000bc4   0x0000001e   Code   RO          193    .text.DL_DMA_setBurstSize  ti_msp_dl_config.o
    0x00000be2   0x00000be2   0x00000002   PAD
    0x00000be4   0x00000be4   0x00000018   Code   RO          123    .text.DL_GPIO_clearInterruptStatus  ti_msp_dl_config.o
    0x00000bfc   0x00000bfc   0x0000001c   Code   RO          467    .text.DL_GPIO_clearInterruptStatus  bsp_motor_hallencoder.o
    0x00000c18   0x00000c18   0x00000014   Code   RO          117    .text.DL_GPIO_clearPins  ti_msp_dl_config.o
    0x00000c2c   0x00000c2c   0x00000014   Code   RO          334    .text.DL_GPIO_clearPins  hw_lcd.o
    0x00000c40   0x00000c40   0x00000014   Code   RO          484    .text.DL_GPIO_clearPins  bsp_tb6612.o
    0x00000c54   0x00000c54   0x00000018   Code   RO          105    .text.DL_GPIO_enableHiZ  ti_msp_dl_config.o
    0x00000c6c   0x00000c6c   0x0000001c   Code   RO          125    .text.DL_GPIO_enableInterrupt  ti_msp_dl_config.o
    0x00000c88   0x00000c88   0x00000018   Code   RO          101    .text.DL_GPIO_enableOutput  ti_msp_dl_config.o
    0x00000ca0   0x00000ca0   0x00000014   Code   RO           87    .text.DL_GPIO_enablePower  ti_msp_dl_config.o
    0x00000cb4   0x00000cb4   0x00000018   Code   RO          463    .text.DL_GPIO_getEnabledInterruptStatus  bsp_motor_hallencoder.o
    0x00000ccc   0x00000ccc   0x00000030   Code   RO          111    .text.DL_GPIO_initDigitalInputFeatures  ti_msp_dl_config.o
    0x00000cfc   0x00000cfc   0x00000014   Code   RO          113    .text.DL_GPIO_initDigitalOutput  ti_msp_dl_config.o
    0x00000d10   0x00000d10   0x0000002c   Code   RO          109    .text.DL_GPIO_initDigitalOutputFeatures  ti_msp_dl_config.o
    0x00000d3c   0x00000d3c   0x00000014   Code   RO           97    .text.DL_GPIO_initPeripheralAnalogFunction  ti_msp_dl_config.o
    0x00000d50   0x00000d50   0x00000018   Code   RO          107    .text.DL_GPIO_initPeripheralInputFunction  ti_msp_dl_config.o
    0x00000d68   0x00000d68   0x00000038   Code   RO          103    .text.DL_GPIO_initPeripheralInputFunctionFeatures  ti_msp_dl_config.o
    0x00000da0   0x00000da0   0x0000001c   Code   RO           99    .text.DL_GPIO_initPeripheralOutputFunction  ti_msp_dl_config.o
    0x00000dbc   0x00000dbc   0x00000016   Code   RO          465    .text.DL_GPIO_readPins  bsp_motor_hallencoder.o
    0x00000dd2   0x00000dd2   0x00000002   PAD
    0x00000dd4   0x00000dd4   0x00000010   Code   RO           77    .text.DL_GPIO_reset  ti_msp_dl_config.o
    0x00000de4   0x00000de4   0x0000001c   Code   RO          119    .text.DL_GPIO_setLowerPinsPolarity  ti_msp_dl_config.o
    0x00000e00   0x00000e00   0x00000014   Code   RO          115    .text.DL_GPIO_setPins  ti_msp_dl_config.o
    0x00000e14   0x00000e14   0x00000014   Code   RO          336    .text.DL_GPIO_setPins  hw_lcd.o
    0x00000e28   0x00000e28   0x00000018   Code   RO          480    .text.DL_GPIO_setPins  bsp_tb6612.o
    0x00000e40   0x00000e40   0x00000018   Code   RO          121    .text.DL_GPIO_setUpperPinsPolarity  ti_msp_dl_config.o
    0x00000e58   0x00000e58   0x00000018   Code   RO          153    .text.DL_I2C_enableAnalogGlitchFilter  ti_msp_dl_config.o
    0x00000e70   0x00000e70   0x00000014   Code   RO          165    .text.DL_I2C_enableController  ti_msp_dl_config.o
    0x00000e84   0x00000e84   0x00000018   Code   RO          163    .text.DL_I2C_enableControllerClockStretching  ti_msp_dl_config.o
    0x00000e9c   0x00000e9c   0x00000018   Code   RO           91    .text.DL_I2C_enablePower  ti_msp_dl_config.o
    0x00000eb4   0x00000eb4   0x0000002c   Code   RO          622    .text.DL_I2C_fillControllerTXFIFO  driverlib.a(dl_i2c.o)
    0x00000ee0   0x00000ee0   0x00000024   Code   RO          624    .text.DL_I2C_flushControllerTXFIFO  driverlib.a(dl_i2c.o)
    0x00000f04   0x00000f04   0x00000014   Code   RO          554    .text.DL_I2C_getControllerStatus  hw_i2c.o
    0x00000f18   0x00000f18   0x00000014   Code   RO          562    .text.DL_I2C_receiveControllerData  hw_i2c.o
    0x00000f2c   0x00000f2c   0x00000010   Code   RO           81    .text.DL_I2C_reset  ti_msp_dl_config.o
    0x00000f3c   0x00000f3c   0x00000010   Code   RO          155    .text.DL_I2C_resetControllerTransfer  ti_msp_dl_config.o
    0x00000f4c   0x00000f4c   0x00000026   Code   RO          151    .text.DL_I2C_setAnalogGlitchFilterPulseWidth  ti_msp_dl_config.o
    0x00000f72   0x00000f72   0x00000026   Code   RO          618    .text.DL_I2C_setClockConfig  driverlib.a(dl_i2c.o)
    0x00000f98   0x00000f98   0x00000024   Code   RO          161    .text.DL_I2C_setControllerRXFIFOThreshold  ti_msp_dl_config.o
    0x00000fbc   0x00000fbc   0x00000028   Code   RO          159    .text.DL_I2C_setControllerTXFIFOThreshold  ti_msp_dl_config.o
    0x00000fe4   0x00000fe4   0x00000018   Code   RO          157    .text.DL_I2C_setTimerPeriod  ti_msp_dl_config.o
    0x00000ffc   0x00000ffc   0x00000050   Code   RO          556    .text.DL_I2C_startControllerTransfer  hw_i2c.o
    0x0000104c   0x0000104c   0x00000018   Code   RO          185    .text.DL_SPI_enable  ti_msp_dl_config.o
    0x00001064   0x00001064   0x00000018   Code   RO           95    .text.DL_SPI_enablePower  ti_msp_dl_config.o
    0x0000107c   0x0000107c   0x00000044   Code   RO          650    .text.DL_SPI_init   driverlib.a(dl_spi.o)
    0x000010c0   0x000010c0   0x00000018   Code   RO          330    .text.DL_SPI_isBusy  hw_lcd.o
    0x000010d8   0x000010d8   0x00000018   Code   RO           85    .text.DL_SPI_reset  ti_msp_dl_config.o
    0x000010f0   0x000010f0   0x00000020   Code   RO          181    .text.DL_SPI_setBitRateSerialClockDivider  ti_msp_dl_config.o
    0x00001110   0x00001110   0x00000012   Code   RO          652    .text.DL_SPI_setClockConfig  driverlib.a(dl_spi.o)
    0x00001122   0x00001122   0x00000002   PAD
    0x00001124   0x00001124   0x00000030   Code   RO          183    .text.DL_SPI_setFIFOThreshold  ti_msp_dl_config.o
    0x00001154   0x00001154   0x00000016   Code   RO          328    .text.DL_SPI_transmitData8  hw_lcd.o
    0x0000116a   0x0000116a   0x00000002   PAD
    0x0000116c   0x0000116c   0x000000c0   Code   RO          845    .text.DL_SYSCTL_configSYSPLL  driverlib.a(dl_sysctl_mspm0g1x0x_g3x0x.o)
    0x0000122c   0x0000122c   0x0000000c   Code   RO          133    .text.DL_SYSCTL_disableHFXT  ti_msp_dl_config.o
    0x00001238   0x00001238   0x00000014   Code   RO          135    .text.DL_SYSCTL_disableSYSPLL  ti_msp_dl_config.o
    0x0000124c   0x0000124c   0x00000010   Code   RO          139    .text.DL_SYSCTL_enableMFCLK  ti_msp_dl_config.o
    0x0000125c   0x0000125c   0x00000018   Code   RO          127    .text.DL_SYSCTL_setBORThreshold  ti_msp_dl_config.o
    0x00001274   0x00001274   0x0000001c   Code   RO          129    .text.DL_SYSCTL_setFlashWaitState  ti_msp_dl_config.o
    0x00001290   0x00001290   0x00000050   Code   RO          859    .text.DL_SYSCTL_setHFCLKSourceHFXTParams  driverlib.a(dl_sysctl_mspm0g1x0x_g3x0x.o)
    0x000012e0   0x000012e0   0x0000001c   Code   RO          131    .text.DL_SYSCTL_setSYSOSCFreq  ti_msp_dl_config.o
    0x000012fc   0x000012fc   0x00000018   Code   RO          137    .text.DL_SYSCTL_setULPCLKDivider  ti_msp_dl_config.o
    0x00001314   0x00001314   0x00000028   Code   RO          853    .text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK  driverlib.a(dl_sysctl_mspm0g1x0x_g3x0x.o)
    0x0000133c   0x0000133c   0x0000000c   Code   RO          197    .text.DL_SYSTICK_enable  ti_msp_dl_config.o
    0x00001348   0x00001348   0x00000028   Code   RO          195    .text.DL_SYSTICK_init  ti_msp_dl_config.o
    0x00001370   0x00001370   0x00000014   Code   RO          145    .text.DL_Timer_enableClock  ti_msp_dl_config.o
    0x00001384   0x00001384   0x00000018   Code   RO          149    .text.DL_Timer_enableInterrupt  ti_msp_dl_config.o
    0x0000139c   0x0000139c   0x00000018   Code   RO           89    .text.DL_Timer_enablePower  ti_msp_dl_config.o
    0x000013b4   0x000013b4   0x00000012   Code   RO           12    .text.DL_Timer_getPendingInterrupt  empty.o
    0x000013c6   0x000013c6   0x00000012   Code   RO          501    .text.DL_Timer_getPendingInterrupt  hw_timer.o
    0x000013d8   0x000013d8   0x00000108   Code   RO          784    .text.DL_Timer_initFourCCPWMMode  driverlib.a(dl_timer.o)
    0x000014e0   0x000014e0   0x000000ec   Code   RO          708    .text.DL_Timer_initTimerMode  driverlib.a(dl_timer.o)
    0x000015cc   0x000015cc   0x00000010   Code   RO           79    .text.DL_Timer_reset  ti_msp_dl_config.o
    0x000015dc   0x000015dc   0x00000014   Code   RO          147    .text.DL_Timer_setCCPDirection  ti_msp_dl_config.o
    0x000015f0   0x000015f0   0x0000001c   Code   RO          750    .text.DL_Timer_setCaptCompUpdateMethod  driverlib.a(dl_timer.o)
    0x0000160c   0x0000160c   0x00000018   Code   RO          758    .text.DL_Timer_setCaptureCompareOutCtl  driverlib.a(dl_timer.o)
    0x00001624   0x00001624   0x00000010   Code   RO          710    .text.DL_Timer_setCaptureCompareValue  driverlib.a(dl_timer.o)
    0x00001634   0x00001634   0x00000018   Code   RO          704    .text.DL_Timer_setClockConfig  driverlib.a(dl_timer.o)
    0x0000164c   0x0000164c   0x0000003c   Code   RO          143    .text.DL_Timer_setCounterControl  ti_msp_dl_config.o
    0x00001688   0x00001688   0x00000016   Code   RO          179    .text.DL_UART_enable  ti_msp_dl_config.o
    0x0000169e   0x0000169e   0x00000018   Code   RO          173    .text.DL_UART_enableFIFOs  ti_msp_dl_config.o
    0x000016b6   0x000016b6   0x00000002   PAD
    0x000016b8   0x000016b8   0x0000001c   Code   RO          171    .text.DL_UART_enableInterrupt  ti_msp_dl_config.o
    0x000016d4   0x000016d4   0x00000018   Code   RO           93    .text.DL_UART_enablePower  ti_msp_dl_config.o
    0x000016ec   0x000016ec   0x00000012   Code   RO          271    .text.DL_UART_getPendingInterrupt  usart.o
    0x000016fe   0x000016fe   0x00000002   PAD
    0x00001700   0x00001700   0x00000048   Code   RO          805    .text.DL_UART_init  driverlib.a(dl_uart.o)
    0x00001748   0x00001748   0x00000014   Code   RO          259    .text.DL_UART_isBusy  usart.o
    0x0000175c   0x0000175c   0x00000018   Code   RO           83    .text.DL_UART_reset  ti_msp_dl_config.o
    0x00001774   0x00001774   0x0000004c   Code   RO          169    .text.DL_UART_setBaudRateDivisor  ti_msp_dl_config.o
    0x000017c0   0x000017c0   0x00000012   Code   RO          807    .text.DL_UART_setClockConfig  driverlib.a(dl_uart.o)
    0x000017d2   0x000017d2   0x0000001e   Code   RO          167    .text.DL_UART_setOversampling  ti_msp_dl_config.o
    0x000017f0   0x000017f0   0x00000024   Code   RO          175    .text.DL_UART_setRXFIFOThreshold  ti_msp_dl_config.o
    0x00001814   0x00001814   0x00000028   Code   RO          177    .text.DL_UART_setTXFIFOThreshold  ti_msp_dl_config.o
    0x0000183c   0x0000183c   0x00000016   Code   RO          261    .text.DL_UART_transmitData  usart.o
    0x00001852   0x00001852   0x00000002   PAD
    0x00001854   0x00001854   0x00000018   Code   RO          823    .text.DL_UART_transmitDataCheck  driverlib.a(dl_uart.o)
    0x0000186c   0x0000186c   0x00000130   Code   RO          461    .text.GROUP1_IRQHandler  bsp_motor_hallencoder.o
    0x0000199c   0x0000199c   0x00000018   Code   RO          533    .text.IIC_Get_Digtal  hardware_iic.o
    0x000019b4   0x000019b4   0x0000001c   Code   RO          523    .text.IIC_ReadByte  hardware_iic.o
    0x000019d0   0x000019d0   0x0000002a   Code   RO          512    .text.Kalman_Init   filter.o
    0x000019fa   0x000019fa   0x0000006c   Code   RO          514    .text.Kalman_Update  filter.o
    0x00001a66   0x00001a66   0x00000014   Code   RO          338    .text.LCD_WR_DATA8  hw_lcd.o
    0x00001a7a   0x00001a7a   0x00000002   PAD
    0x00001a7c   0x00001a7c   0x0000002c   Code   RO          342    .text.LCD_WR_REG    hw_lcd.o
    0x00001aa8   0x00001aa8   0x00000030   Code   RO          332    .text.LCD_Writ_Bus  hw_lcd.o
    0x00001ad8   0x00001ad8   0x000000b8   Code   RO          434    .text.PID_Calculate  pid.o
    0x00001b90   0x00001b90   0x0000002c   Code   RO          432    .text.PID_Init      pid.o
    0x00001bbc   0x00001bbc   0x00000160   Code   RO            8    .text.PID_operation  empty.o
    0x00001d1c   0x00001d1c   0x00000032   Code   RO          531    .text.Ping          hardware_iic.o
    0x00001d4e   0x00001d4e   0x00000002   PAD
    0x00001d50   0x00001d50   0x0000002c   Code   RO          187    .text.SYSCFG_DL_DMA_CH0_init  ti_msp_dl_config.o
    0x00001d7c   0x00001d7c   0x00000014   Code   RO           69    .text.SYSCFG_DL_DMA_init  ti_msp_dl_config.o
    0x00001d90   0x00001d90   0x000001e4   Code   RO           51    .text.SYSCFG_DL_GPIO_init  ti_msp_dl_config.o
    0x00001f74   0x00001f74   0x00000054   Code   RO           63    .text.SYSCFG_DL_I2C_0_init  ti_msp_dl_config.o
    0x00001fc8   0x00001fc8   0x0000008c   Code   RO           57    .text.SYSCFG_DL_PWM_0_init  ti_msp_dl_config.o
    0x00002054   0x00002054   0x00000068   Code   RO           55    .text.SYSCFG_DL_PWM_LED_init  ti_msp_dl_config.o
    0x000020bc   0x000020bc   0x0000003c   Code   RO           67    .text.SYSCFG_DL_SPI_LCD_init  ti_msp_dl_config.o
    0x000020f8   0x000020f8   0x00000058   Code   RO           53    .text.SYSCFG_DL_SYSCTL_init  ti_msp_dl_config.o
    0x00002150   0x00002150   0x0000000e   Code   RO           71    .text.SYSCFG_DL_SYSTICK_init  ti_msp_dl_config.o
    0x0000215e   0x0000215e   0x00000002   PAD
    0x00002160   0x00002160   0x00000030   Code   RO           59    .text.SYSCFG_DL_TIMER_0_init  ti_msp_dl_config.o
    0x00002190   0x00002190   0x00000038   Code   RO           61    .text.SYSCFG_DL_TIMER_TICK_init  ti_msp_dl_config.o
    0x000021c8   0x000021c8   0x00000064   Code   RO           65    .text.SYSCFG_DL_UART_0_init  ti_msp_dl_config.o
    0x0000222c   0x0000222c   0x0000005c   Code   RO           47    .text.SYSCFG_DL_init  ti_msp_dl_config.o
    0x00002288   0x00002288   0x000000a4   Code   RO           49    .text.SYSCFG_DL_initPower  ti_msp_dl_config.o
    0x0000232c   0x0000232c   0x00000020   Code   RO          499    .text.TIMA0_IRQHandler  hw_timer.o
    0x0000234c   0x0000234c   0x000000c0   Code   RO           10    .text.TIMG0_IRQHandler  empty.o
    0x0000240c   0x0000240c   0x00000040   Code   RO          269    .text.UART0_IRQHandler  usart.o
    0x0000244c   0x0000244c   0x0000001c   Code   RO          589    .text.UpdatePosition  sensor.o
    0x00002468   0x00002468   0x00000028   Code   RO            4    .text.__NVIC_ClearPendingIRQ  empty.o
    0x00002490   0x00002490   0x00000028   Code   RO          445    .text.__NVIC_ClearPendingIRQ  bsp_motor_hallencoder.o
    0x000024b8   0x000024b8   0x0000002c   Code   RO          495    .text.__NVIC_ClearPendingIRQ  hw_timer.o
    0x000024e4   0x000024e4   0x00000028   Code   RO            6    .text.__NVIC_EnableIRQ  empty.o
    0x0000250c   0x0000250c   0x00000028   Code   RO          447    .text.__NVIC_EnableIRQ  bsp_motor_hallencoder.o
    0x00002534   0x00002534   0x0000002c   Code   RO          497    .text.__NVIC_EnableIRQ  hw_timer.o
    0x00002560   0x00002560   0x00000084   Code   RO          141    .text.__NVIC_SetPriority  ti_msp_dl_config.o
    0x000025e4   0x000025e4   0x00000016   Code   RO          578    .text.delay_ms      time.o
    0x000025fa   0x000025fa   0x00000002   PAD
    0x000025fc   0x000025fc   0x00000074   Code   RO          576    .text.delay_us      time.o
    0x00002670   0x00002670   0x00000016   Code   RO          443    .text.encoder_init  bsp_motor_hallencoder.o
    0x00002686   0x00002686   0x00000002   PAD
    0x00002688   0x00002688   0x00000018   Code   RO          457    .text.encoder_update_L  bsp_motor_hallencoder.o
    0x000026a0   0x000026a0   0x00000018   Code   RO          459    .text.encoder_update_R  bsp_motor_hallencoder.o
    0x000026b8   0x000026b8   0x0000001c   Code   RO          449    .text.get_encoder_count_L  bsp_motor_hallencoder.o
    0x000026d4   0x000026d4   0x00000020   Code   RO          451    .text.get_encoder_count_R  bsp_motor_hallencoder.o
    0x000026f4   0x000026f4   0x0000007c   Code   RO          541    .text.grayscale_detection_sensor  hardware_iic.o
    0x00002770   0x00002770   0x000000b0   Code   RO          560    .text.hardware_IIC_ReadByte  hw_i2c.o
    0x00002820   0x00002820   0x000001d0   Code   RO          346    .text.lcd_init      hw_lcd.o
    0x000029f0   0x000029f0   0x000000f0   Code   RO            2    .text.main          empty.o
    0x00002ae0   0x00002ae0   0x0000002c   Code   RO          326    .text.spi_write_bus  hw_lcd.o
    0x00002b0c   0x00002b0c   0x00000016   Code   RO          493    .text.timer_init    hw_timer.o
    0x00002b22   0x00002b22   0x00000002   PAD
    0x00002b24   0x00002b24   0x00000028   Code   RO          257    .text.uart0_send_char  usart.o
    0x00002b4c   0x00002b4c   0x0000003c   Code   RO          263    .text.uart0_send_string  usart.o
    0x00002b88   0x00002b88   0x00000074   Code   RO          267    .text.uart_send     usart.o
    0x00002bfc   0x00002bfc   0x00000028   Code   RO          900    i.__0sprintf        mc_p.l(printfa.o)
    0x00002c24   0x00002c24   0x0000002e   Code   RO         1000    i.__ARM_clz         mf_p.l(depilogue.o)
    0x00002c52   0x00002c52   0x00000006   PAD
    0x00002c58   0x00002c58   0x0000000e   Code   RO         1012    i.__scatterload_copy  mc_p.l(handlers.o)
    0x00002c66   0x00002c66   0x00000002   PAD
    0x00002c68   0x00002c68   0x00000002   Code   RO         1013    i.__scatterload_null  mc_p.l(handlers.o)
    0x00002c6a   0x00002c6a   0x00000006   PAD
    0x00002c70   0x00002c70   0x0000000e   Code   RO         1014    i.__scatterload_zeroinit  mc_p.l(handlers.o)
    0x00002c7e   0x00002c7e   0x00000002   PAD
    0x00002c80   0x00002c80   0x00000174   Code   RO          905    i._fp_digits        mc_p.l(printfa.o)
    0x00002df4   0x00002df4   0x000006ec   Code   RO          906    i._printf_core      mc_p.l(printfa.o)
    0x000034e0   0x000034e0   0x00000020   Code   RO          907    i._printf_post_padding  mc_p.l(printfa.o)
    0x00003500   0x00003500   0x0000002c   Code   RO          908    i._printf_pre_padding  mc_p.l(printfa.o)
    0x0000352c   0x0000352c   0x0000000a   Code   RO          910    i._sputc            mc_p.l(printfa.o)
    0x00003536   0x00003536   0x00000002   PAD
    0x00003538   0x00003538   0x00000018   Data   RO          219    .rodata.gDMA_CH0Config  ti_msp_dl_config.o
    0x00003550   0x00003550   0x00000002   Data   RO          214    .rodata.gI2C_0ClockConfig  ti_msp_dl_config.o
    0x00003552   0x00003552   0x00000003   Data   RO          208    .rodata.gPWM_0ClockConfig  ti_msp_dl_config.o
    0x00003555   0x00003555   0x00000003   PAD
    0x00003558   0x00003558   0x00000008   Data   RO          209    .rodata.gPWM_0Config  ti_msp_dl_config.o
    0x00003560   0x00003560   0x00000003   Data   RO          206    .rodata.gPWM_LEDClockConfig  ti_msp_dl_config.o
    0x00003563   0x00003563   0x00000001   PAD
    0x00003564   0x00003564   0x00000008   Data   RO          207    .rodata.gPWM_LEDConfig  ti_msp_dl_config.o
    0x0000356c   0x0000356c   0x00000002   Data   RO          217    .rodata.gSPI_LCD_clockConfig  ti_msp_dl_config.o
    0x0000356e   0x0000356e   0x0000000a   Data   RO          218    .rodata.gSPI_LCD_config  ti_msp_dl_config.o
    0x00003578   0x00003578   0x00000028   Data   RO          205    .rodata.gSYSPLLConfig  ti_msp_dl_config.o
    0x000035a0   0x000035a0   0x00000003   Data   RO          210    .rodata.gTIMER_0ClockConfig  ti_msp_dl_config.o
    0x000035a3   0x000035a3   0x00000001   PAD
    0x000035a4   0x000035a4   0x00000014   Data   RO          211    .rodata.gTIMER_0TimerConfig  ti_msp_dl_config.o
    0x000035b8   0x000035b8   0x00000003   Data   RO          212    .rodata.gTIMER_TICKClockConfig  ti_msp_dl_config.o
    0x000035bb   0x000035bb   0x00000001   PAD
    0x000035bc   0x000035bc   0x00000014   Data   RO          213    .rodata.gTIMER_TICKTimerConfig  ti_msp_dl_config.o
    0x000035d0   0x000035d0   0x00000002   Data   RO          215    .rodata.gUART_0ClockConfig  ti_msp_dl_config.o
    0x000035d2   0x000035d2   0x0000000a   Data   RO          216    .rodata.gUART_0Config  ti_msp_dl_config.o
    0x000035dc   0x000035dc   0x00000020   Data   RO          591    .rodata.position_weights  sensor.o
    0x000035fc   0x000035fc   0x00000010   Data   RO           27    .rodata.str1.1      empty.o
    0x0000360c   0x0000360c   0x00000039   Data   RO          544    .rodata.str1.1      hardware_iic.o
    0x00003645   0x00003645   0x00000003   PAD
    0x00003648   0x00003648   0x00000020   Data   RO         1011    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x20200000, Load base: 0x00003668, Size: 0x000009e8, Max: 0x00008000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20200000   0x00003668   0x00000004   Data   RW           28    .data.TIMG0_IRQHandler.timer_count  empty.o
    0x20200004   0x0000366c   0x00000001   Data   RW          273    .data.uart_tx_dma_complete_flag  usart.o
    0x20200005   0x0000366d   0x00000003   PAD
    0x20200008        -       0x00000004   Zero   RW          503    .bss.Num_L          hw_timer.o
    0x2020000c        -       0x00000004   Zero   RW          504    .bss.Num_R          hw_timer.o
    0x20200010        -       0x00000001   Zero   RW           16    .bss.PID_flash_time  empty.o
    0x20200011   0x0000366d   0x00000003   PAD
    0x20200014        -       0x00000004   Zero   RW           24    .bss.PID_operation.total_left_speed  empty.o
    0x20200018        -       0x00000004   Zero   RW           25    .bss.PID_operation.total_right_speed  empty.o
    0x2020001c        -       0x00000004   Zero   RW           26    .bss.PID_operation.total_speed  empty.o
    0x20200020        -       0x00000001   Zero   RW           15    .bss.bmq_flash_time  empty.o
    0x20200021   0x0000366d   0x00000003   PAD
    0x20200024        -       0x000000a0   Zero   RW          202    .bss.gPWM_0Backup   ti_msp_dl_config.o
    0x202000c4        -       0x000000a0   Zero   RW          201    .bss.gPWM_LEDBackup  ti_msp_dl_config.o
    0x20200164        -       0x00000028   Zero   RW          204    .bss.gSPI_LCDBackup  ti_msp_dl_config.o
    0x2020018c        -       0x000000bc   Zero   RW          203    .bss.gTIMER_TICKBackup  ti_msp_dl_config.o
    0x20200248        -       0x00000002   Zero   RW          275    .bss.has_send_len   usart.o
    0x2020024a        -       0x00000002   Zero   RW           17    .bss.j              empty.o
    0x2020024c        -       0x00000001   Zero   RW           14    .bss.led_flash_time  empty.o
    0x2020024d   0x0000366d   0x00000003   PAD
    0x20200250        -       0x00000010   Zero   RW           22    .bss.left_kalman    empty.o
    0x20200260        -       0x00000014   Zero   RW           18    .bss.left_speed_pid  empty.o
    0x20200274   0x0000366d   0x00000004   PAD
    0x20200278        -       0x00000010   Zero   RW          469    .bss.motor_encoder_L  bsp_motor_hallencoder.o
    0x20200288        -       0x00000010   Zero   RW          470    .bss.motor_encoder_R  bsp_motor_hallencoder.o
    0x20200298        -       0x00000010   Zero   RW           21    .bss.posion_kalman  empty.o
    0x202002a8        -       0x00000014   Zero   RW           20    .bss.posion_pid     empty.o
    0x202002bc        -       0x00000010   Zero   RW           23    .bss.right_kalman   empty.o
    0x202002cc        -       0x00000014   Zero   RW           19    .bss.right_speed_pid  empty.o
    0x202002e0        -       0x00000100   Zero   RW          276    .bss.rx_buff        usart.o
    0x202003e0        -       0x00000002   Zero   RW          274    .bss.send_len       usart.o
    0x202003e2        -       0x00000200   Zero   RW           29    .bss.temporary_buffer  empty.o
    0x202005e2   0x0000366d   0x00000006   PAD
    0x202005e8        -       0x00000400   Zero   RW           37    STACK               startup_mspm0g350x_uvision.o



  Load Region LR_BCR (Base: 0x41c00000, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BCR_CONFIG (Exec base: 0x41c00000, Load base: 0x41c00000, Size: 0x00000000, Max: 0x000000ff, ABSOLUTE)

    **** No section assigned to this execution region ****



  Load Region LR_BSL (Base: 0x41c00100, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BSL_CONFIG (Exec base: 0x41c00100, Load base: 0x41c00100, Size: 0x00000000, Max: 0x00000080, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       588         32          0          0         32       6700   bsp_motor_hallencoder.o
       220         12          0          0          0       5954   bsp_tb6612.o
       882        152         16          4        637       7046   empty.o
       150          0          0          0          0       1009   filter.o
       226         16         57          0          0       2222   hardware_iic.o
       336         28          0          0          0       5014   hw_i2c.o
       706         28          0          0          0      15921   hw_lcd.o
       160         12          0          0          8       5500   hw_timer.o
       228          4          0          0          0       1078   pid.o
       156          4         32          0          0       1057   sensor.o
        20          4        192          0       1024        616   startup_mspm0g350x_uvision.o
      3298        280        158          0        548      41236   ti_msp_dl_config.o
       138          8          0          0          0       1114   time.o
       340         28          0          1        260       4669   usart.o

    ----------------------------------------------------------------------
      7474        <USER>        <GROUP>          8       2528      99136   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        26          0          9          3         19          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        10          0          0          0          0        803   dl_common.o
        68          4          0          0          0       4510   dl_dma.o
       118          0          0          0          0       8620   dl_i2c.o
        86          8          0          0          0      13518   dl_spi.o
       312         24          0          0          0      12877   dl_sysctl_mspm0g1x0x_g3x0x.o
       592        184          0          0          0      41557   dl_timer.o
       114         12          0          0          0      14163   dl_uart.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        48         10          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        32          0          0          0          0         68   llshl.o
        38          0          0          0          0         68   llsshr.o
        34          0          0          0          0         68   llushr.o
        36          0          0          0          0        100   memseta.o
      2270         94          0          0          0        472   printfa.o
        14          0          0          0          0         60   strlen.o
        62          0          0          0          0         72   uidiv_div0.o
        96          0          0          0          0         84   uldiv.o
        40          2          0          0          0         68   cdrcmple.o
       356          4          0          0          0        140   dadd.o
       240          6          0          0          0         84   ddiv.o
       236          0          0          0          0        216   depilogue.o
        72         10          0          0          0         72   dfixi.o
        64         10          0          0          0         68   dfixul.o
        40          6          0          0          0         68   dflti.o
       208          6          0          0          0         88   dmul.o
        40          0          0          0          0         60   f2d.o
       178          0          0          0          0        108   fadd.o
        28          0          0          0          0         60   fcmple.o
       124          0          0          0          0         72   fdiv.o
       130          0          0          0          0        144   fepilogue.o
        50          0          0          0          0         60   ffixi.o
        22          0          0          0          0         68   fflti.o
       122          0          0          0          0         72   fmul.o

    ----------------------------------------------------------------------
      5958        <USER>          <GROUP>          0          0      98556   Library Totals
        28          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      1300        232          0          0          0      96048   driverlib.a
      2680        112          0          0          0       1060   mc_p.l
      1950         44          0          0          0       1448   mf_p.l

    ----------------------------------------------------------------------
      5958        <USER>          <GROUP>          0          0      98556   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     13432        996        496          8       2528     196224   Grand Totals
     13432        996        496          8       2528     196224   ELF Image Totals
     13432        996        496          8          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                13928 (  13.60kB)
    Total RW  Size (RW Data + ZI Data)              2536 (   2.48kB)
    Total ROM Size (Code + RO Data + RW Data)      13936 (  13.61kB)

==============================================================================

